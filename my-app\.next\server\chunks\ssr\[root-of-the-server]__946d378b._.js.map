{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/components/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\n\nexport default function HeroSection() {\n  const heroRef = useRef(null);\n  const titleRef = useRef(null);\n  const subtitleRef = useRef(null);\n  const buttonsRef = useRef(null);\n  const backgroundRef = useRef(null);\n\n  useEffect(() => {\n    const tl = gsap.timeline();\n    \n    // Background animation\n    gsap.set(backgroundRef.current, { scale: 1.1, opacity: 0 });\n    tl.to(backgroundRef.current, { scale: 1, opacity: 1, duration: 1.5, ease: \"power2.out\" });\n    \n    // Text animations\n    tl.fromTo(titleRef.current, \n      { y: 100, opacity: 0 },\n      { y: 0, opacity: 1, duration: 1, ease: \"power3.out\" },\n      \"-=1\"\n    );\n    \n    tl.fromTo(subtitleRef.current,\n      { y: 50, opacity: 0 },\n      { y: 0, opacity: 1, duration: 0.8, ease: \"power2.out\" },\n      \"-=0.5\"\n    );\n    \n    tl.fromTo(buttonsRef.current.children,\n      { y: 30, opacity: 0 },\n      { y: 0, opacity: 1, duration: 0.6, stagger: 0.2, ease: \"power2.out\" },\n      \"-=0.3\"\n    );\n\n    // Floating animation for background elements\n    gsap.to('.floating-element', {\n      y: -20,\n      duration: 3,\n      ease: \"power1.inOut\",\n      yoyo: true,\n      repeat: -1,\n      stagger: 0.5\n    });\n  }, []);\n\n  return (\n    <section ref={heroRef} className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-purple-900\">\n      {/* Background Elements */}\n      <div ref={backgroundRef} className=\"absolute inset-0\">\n        <div className=\"floating-element absolute top-20 left-10 w-32 h-32 bg-blue-200 dark:bg-blue-800 rounded-full opacity-20 blur-xl\"></div>\n        <div className=\"floating-element absolute top-40 right-20 w-24 h-24 bg-purple-200 dark:bg-purple-800 rounded-full opacity-30 blur-lg\"></div>\n        <div className=\"floating-element absolute bottom-32 left-1/4 w-40 h-40 bg-green-200 dark:bg-green-800 rounded-full opacity-15 blur-2xl\"></div>\n        <div className=\"floating-element absolute bottom-20 right-1/3 w-28 h-28 bg-orange-200 dark:bg-orange-800 rounded-full opacity-25 blur-xl\"></div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 max-w-6xl mx-auto px-6 text-center\">\n        <div className=\"mb-8\">\n          <h1 \n            ref={titleRef}\n            className=\"text-5xl md:text-7xl font-bold mb-6 leading-tight\"\n          >\n            <span className=\"gradient-text\">AI-Powered</span>\n            <br />\n            <span className=\"text-slate-800 dark:text-white\">Audio to Text</span>\n          </h1>\n          \n          <p \n            ref={subtitleRef}\n            className=\"text-xl md:text-2xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Transform your audio files into accurate text with our advanced AI technology. \n            Supporting <span className=\"font-semibold text-blue-600 dark:text-blue-400\">100+ languages</span> \n            with lightning-fast processing and industry-leading accuracy.\n          </p>\n        </div>\n\n        {/* Download Buttons */}\n        <div ref={buttonsRef} className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n          <button className=\"btn-hover-effect group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center gap-3 min-w-[200px]\">\n            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n            Download for Windows\n          </button>\n          \n          <button className=\"btn-hover-effect group bg-gradient-to-r from-slate-700 to-slate-800 hover:from-slate-800 hover:to-slate-900 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center gap-3 min-w-[200px]\">\n            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n            Download for Mac\n          </button>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">100+</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Languages Supported</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">99.5%</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Accuracy Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">10x</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Faster Processing</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <svg className=\"w-6 h-6 text-slate-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n        </svg>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ;QAExB,uBAAuB;QACvB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,cAAc,OAAO,EAAE;YAAE,OAAO;YAAK,SAAS;QAAE;QACzD,GAAG,EAAE,CAAC,cAAc,OAAO,EAAE;YAAE,OAAO;YAAG,SAAS;YAAG,UAAU;YAAK,MAAM;QAAa;QAEvF,kBAAkB;QAClB,GAAG,MAAM,CAAC,SAAS,OAAO,EACxB;YAAE,GAAG;YAAK,SAAS;QAAE,GACrB;YAAE,GAAG;YAAG,SAAS;YAAG,UAAU;YAAG,MAAM;QAAa,GACpD;QAGF,GAAG,MAAM,CAAC,YAAY,OAAO,EAC3B;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YAAE,GAAG;YAAG,SAAS;YAAG,UAAU;YAAK,MAAM;QAAa,GACtD;QAGF,GAAG,MAAM,CAAC,WAAW,OAAO,CAAC,QAAQ,EACnC;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YAAE,GAAG;YAAG,SAAS;YAAG,UAAU;YAAK,SAAS;YAAK,MAAM;QAAa,GACpE;QAGF,6CAA6C;QAC7C,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,qBAAqB;YAC3B,GAAG,CAAC;YACJ,UAAU;YACV,MAAM;YACN,MAAM;YACN,QAAQ,CAAC;YACT,SAAS;QACX;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,KAAK;QAAS,WAAU;;0BAE/B,8OAAC;gBAAI,KAAK;gBAAe,WAAU;;kCACjC,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,KAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;;0CAGnD,8OAAC;gCACC,KAAK;gCACL,WAAU;;oCACX;kDAEY,8OAAC;wCAAK,WAAU;kDAAiD;;;;;;oCAAqB;;;;;;;;;;;;;kCAMrG,8OAAC;wBAAI,KAAK;wBAAY,WAAU;;0CAC9B,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAwL,UAAS;;;;;;;;;;;oCACxN;;;;;;;0CAIR,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAwL,UAAS;;;;;;;;;;;oCACxN;;;;;;;;;;;;;kCAMV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;0BAM1D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAyB,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAChF,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;AAK/E", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport HeroSection from './components/HeroSection';\nimport FeaturesSection from './components/FeaturesSection';\nimport LanguagesSection from './components/LanguagesSection';\nimport HowItWorksSection from './components/HowItWorksSection';\nimport UseCasesSection from './components/UseCasesSection';\nimport CTASection from './components/CTASection';\nimport FAQSection from './components/FAQSection';\nimport Footer from './components/Footer';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function Home() {\n  useEffect(() => {\n    // Initialize GSAP animations\n    gsap.fromTo('.fade-in',\n      { opacity: 0, y: 50 },\n      {\n        opacity: 1,\n        y: 0,\n        duration: 1,\n        stagger: 0.2,\n        scrollTrigger: {\n          trigger: '.fade-in',\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n  }, []);\n\n  return (\n    <main className=\"min-h-screen\">\n      <HeroSection />\n      <FeaturesSection />\n      <LanguagesSection />\n      <HowItWorksSection />\n      <UseCasesSection />\n      <CTASection />\n      <FAQSection />\n      <Footer />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALA;;;;;;;;;;;;;AAcA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAElB,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,YACV;YAAE,SAAS;YAAG,GAAG;QAAG,GACpB;YACE,SAAS;YACT,GAAG;YACH,UAAU;YACV,SAAS;YACT,eAAe;gBACb,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;IAEJ,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,uIAAA,CAAA,UAAW;;;;;0BACZ,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;0BACD,8OAAC;;;;;;;;;;;AAGP", "debugId": null}}]}