'use client';

import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export default function UseCasesSection() {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const tabsRef = useRef(null);
  const contentRef = useRef(null);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    gsap.fromTo(titleRef.current,
      { y: 50, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      }
    );

    gsap.fromTo(tabsRef.current.children,
      { y: 30, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.6,
        stagger: 0.1,
        scrollTrigger: {
          trigger: tabsRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      }
    );
  }, []);

  useEffect(() => {
    // Animate content change
    gsap.fromTo(contentRef.current,
      { opacity: 0, x: 20 },
      { opacity: 1, x: 0, duration: 0.5, ease: "power2.out" }
    );
  }, [activeTab]);

  const useCases = [
    {
      title: "Business & Meetings",
      icon: "💼",
      description: "Transform meeting recordings, conference calls, and business presentations into searchable text documents.",
      features: [
        "Meeting transcriptions with speaker identification",
        "Conference call summaries and action items",
        "Presentation notes and key points extraction",
        "Client call documentation"
      ],
      image: "📊"
    },
    {
      title: "Education & Learning",
      icon: "🎓",
      description: "Convert lectures, seminars, and educational content into study materials and accessible text formats.",
      features: [
        "Lecture transcriptions for note-taking",
        "Online course subtitle generation",
        "Research interview documentation",
        "Student accessibility support"
      ],
      image: "📚"
    },
    {
      title: "Content Creation",
      icon: "🎬",
      description: "Generate subtitles, captions, and transcripts for videos, podcasts, and multimedia content.",
      features: [
        "YouTube video subtitle creation",
        "Podcast episode transcriptions",
        "Social media content captions",
        "Video accessibility compliance"
      ],
      image: "🎥"
    },
    {
      title: "Legal & Medical",
      icon: "⚖️",
      description: "Accurate transcription of legal proceedings, medical consultations, and professional documentation.",
      features: [
        "Court proceeding documentation",
        "Medical consultation records",
        "Legal interview transcriptions",
        "Compliance and audit trails"
      ],
      image: "🏥"
    },
    {
      title: "Journalism & Media",
      icon: "📰",
      description: "Convert interviews, press conferences, and media content into written articles and reports.",
      features: [
        "Interview transcription and quotes",
        "Press conference documentation",
        "News story research and fact-checking",
        "Media archive digitization"
      ],
      image: "🎤"
    },
    {
      title: "Personal Use",
      icon: "👤",
      description: "Transcribe personal recordings, voice memos, and family memories into text for easy organization.",
      features: [
        "Voice memo transcription",
        "Family story preservation",
        "Personal journal conversion",
        "Travel log documentation"
      ],
      image: "📝"
    }
  ];

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-slate-800 dark:via-slate-900 dark:to-purple-900">
      <div className="max-w-7xl mx-auto px-6">
        <div ref={titleRef} className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="gradient-text">Perfect for Every Use Case</span>
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
            From business meetings to personal projects, our AI transcription tool adapts to your needs
            with industry-specific accuracy and formatting.
          </p>
        </div>

        {/* Tabs */}
        <div ref={tabsRef} className="flex flex-wrap justify-center gap-4 mb-12">
          {useCases.map((useCase, index) => (
            <button
              key={index}
              onClick={() => setActiveTab(index)}
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 flex items-center gap-2 ${
                activeTab === index
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg transform scale-105'
                  : 'bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700 shadow-md hover:shadow-lg'
              }`}
            >
              <span className="text-xl">{useCase.icon}</span>
              <span className="hidden sm:inline">{useCase.title}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div ref={contentRef} className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <div className="space-y-6">
            <div className="flex items-center gap-4 mb-6">
              <div className="text-6xl">{useCases[activeTab].icon}</div>
              <h3 className="text-3xl font-bold text-slate-800 dark:text-white">
                {useCases[activeTab].title}
              </h3>
            </div>
            
            <p className="text-lg text-slate-600 dark:text-slate-400 leading-relaxed">
              {useCases[activeTab].description}
            </p>

            <div className="space-y-3">
              <h4 className="text-xl font-semibold text-slate-800 dark:text-white mb-4">
                Key Features:
              </h4>
              {useCases[activeTab].features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
                  <span className="text-slate-600 dark:text-slate-400">{feature}</span>
                </div>
              ))}
            </div>

            <button className="btn-hover-effect bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 mt-6">
              Try This Use Case
            </button>
          </div>

          {/* Visual Content */}
          <div className="flex justify-center">
            <div className="relative w-80 h-80 bg-gradient-to-br from-white to-slate-100 dark:from-slate-800 dark:to-slate-900 rounded-3xl shadow-2xl flex items-center justify-center border border-slate-200 dark:border-slate-700">
              <div className="text-9xl opacity-80">
                {useCases[activeTab].image}
              </div>
              
              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white text-2xl shadow-lg animate-bounce">
                ✨
              </div>
              
              <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center text-white text-lg shadow-lg animate-pulse">
                🚀
              </div>
              
              <div className="absolute top-1/4 -left-6 w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white text-sm shadow-lg animate-ping">
                ⚡
              </div>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
          <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg">
            <div className="text-2xl font-bold gradient-text mb-2">1M+</div>
            <div className="text-slate-600 dark:text-slate-400 text-sm">Hours Transcribed</div>
          </div>
          <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg">
            <div className="text-2xl font-bold gradient-text mb-2">50K+</div>
            <div className="text-slate-600 dark:text-slate-400 text-sm">Happy Users</div>
          </div>
          <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg">
            <div className="text-2xl font-bold gradient-text mb-2">99.5%</div>
            <div className="text-slate-600 dark:text-slate-400 text-sm">Accuracy Rate</div>
          </div>
          <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg">
            <div className="text-2xl font-bold gradient-text mb-2">24/7</div>
            <div className="text-slate-600 dark:text-slate-400 text-sm">Support</div>
          </div>
        </div>
      </div>
    </section>
  );
}
