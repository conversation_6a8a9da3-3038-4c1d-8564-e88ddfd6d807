{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/components/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\n\nexport default function HeroSection() {\n  const heroRef = useRef(null);\n  const titleRef = useRef(null);\n  const subtitleRef = useRef(null);\n  const buttonsRef = useRef(null);\n  const backgroundRef = useRef(null);\n\n  useEffect(() => {\n    const tl = gsap.timeline();\n    \n    // Background animation\n    gsap.set(backgroundRef.current, { scale: 1.1, opacity: 0 });\n    tl.to(backgroundRef.current, { scale: 1, opacity: 1, duration: 1.5, ease: \"power2.out\" });\n    \n    // Text animations\n    tl.fromTo(titleRef.current, \n      { y: 100, opacity: 0 },\n      { y: 0, opacity: 1, duration: 1, ease: \"power3.out\" },\n      \"-=1\"\n    );\n    \n    tl.fromTo(subtitleRef.current,\n      { y: 50, opacity: 0 },\n      { y: 0, opacity: 1, duration: 0.8, ease: \"power2.out\" },\n      \"-=0.5\"\n    );\n    \n    tl.fromTo(buttonsRef.current.children,\n      { y: 30, opacity: 0 },\n      { y: 0, opacity: 1, duration: 0.6, stagger: 0.2, ease: \"power2.out\" },\n      \"-=0.3\"\n    );\n\n    // Floating animation for background elements\n    gsap.to('.floating-element', {\n      y: -20,\n      duration: 3,\n      ease: \"power1.inOut\",\n      yoyo: true,\n      repeat: -1,\n      stagger: 0.5\n    });\n  }, []);\n\n  return (\n    <section ref={heroRef} className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-purple-900\">\n      {/* Background Elements */}\n      <div ref={backgroundRef} className=\"absolute inset-0\">\n        <div className=\"floating-element absolute top-20 left-10 w-32 h-32 bg-blue-200 dark:bg-blue-800 rounded-full opacity-20 blur-xl\"></div>\n        <div className=\"floating-element absolute top-40 right-20 w-24 h-24 bg-purple-200 dark:bg-purple-800 rounded-full opacity-30 blur-lg\"></div>\n        <div className=\"floating-element absolute bottom-32 left-1/4 w-40 h-40 bg-green-200 dark:bg-green-800 rounded-full opacity-15 blur-2xl\"></div>\n        <div className=\"floating-element absolute bottom-20 right-1/3 w-28 h-28 bg-orange-200 dark:bg-orange-800 rounded-full opacity-25 blur-xl\"></div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 max-w-6xl mx-auto px-6 text-center\">\n        <div className=\"mb-8\">\n          <h1 \n            ref={titleRef}\n            className=\"text-5xl md:text-7xl font-bold mb-6 leading-tight\"\n          >\n            <span className=\"gradient-text\">AI-Powered</span>\n            <br />\n            <span className=\"text-slate-800 dark:text-white\">Audio to Text</span>\n          </h1>\n          \n          <p \n            ref={subtitleRef}\n            className=\"text-xl md:text-2xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Transform your audio files into accurate text with our advanced AI technology. \n            Supporting <span className=\"font-semibold text-blue-600 dark:text-blue-400\">100+ languages</span> \n            with lightning-fast processing and industry-leading accuracy.\n          </p>\n        </div>\n\n        {/* Download Buttons */}\n        <div ref={buttonsRef} className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n          <button className=\"btn-hover-effect group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center gap-3 min-w-[200px]\">\n            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n            Download for Windows\n          </button>\n          \n          <button className=\"btn-hover-effect group bg-gradient-to-r from-slate-700 to-slate-800 hover:from-slate-800 hover:to-slate-900 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center gap-3 min-w-[200px]\">\n            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n            Download for Mac\n          </button>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">100+</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Languages Supported</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">99.5%</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Accuracy Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">10x</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Faster Processing</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <svg className=\"w-6 h-6 text-slate-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n        </svg>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ;QAExB,uBAAuB;QACvB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,cAAc,OAAO,EAAE;YAAE,OAAO;YAAK,SAAS;QAAE;QACzD,GAAG,EAAE,CAAC,cAAc,OAAO,EAAE;YAAE,OAAO;YAAG,SAAS;YAAG,UAAU;YAAK,MAAM;QAAa;QAEvF,kBAAkB;QAClB,GAAG,MAAM,CAAC,SAAS,OAAO,EACxB;YAAE,GAAG;YAAK,SAAS;QAAE,GACrB;YAAE,GAAG;YAAG,SAAS;YAAG,UAAU;YAAG,MAAM;QAAa,GACpD;QAGF,GAAG,MAAM,CAAC,YAAY,OAAO,EAC3B;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YAAE,GAAG;YAAG,SAAS;YAAG,UAAU;YAAK,MAAM;QAAa,GACtD;QAGF,GAAG,MAAM,CAAC,WAAW,OAAO,CAAC,QAAQ,EACnC;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YAAE,GAAG;YAAG,SAAS;YAAG,UAAU;YAAK,SAAS;YAAK,MAAM;QAAa,GACpE;QAGF,6CAA6C;QAC7C,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,qBAAqB;YAC3B,GAAG,CAAC;YACJ,UAAU;YACV,MAAM;YACN,MAAM;YACN,QAAQ,CAAC;YACT,SAAS;QACX;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,KAAK;QAAS,WAAU;;0BAE/B,8OAAC;gBAAI,KAAK;gBAAe,WAAU;;kCACjC,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,KAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;;0CAGnD,8OAAC;gCACC,KAAK;gCACL,WAAU;;oCACX;kDAEY,8OAAC;wCAAK,WAAU;kDAAiD;;;;;;oCAAqB;;;;;;;;;;;;;kCAMrG,8OAAC;wBAAI,KAAK;wBAAY,WAAU;;0CAC9B,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAwL,UAAS;;;;;;;;;;;oCACxN;;;;;;;0CAIR,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAwL,UAAS;;;;;;;;;;;oCACxN;;;;;;;;;;;;;kCAMV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;0BAM1D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAyB,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAChF,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;AAK/E", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/components/FeaturesSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function FeaturesSection() {\n  const sectionRef = useRef(null);\n  const titleRef = useRef(null);\n  const cardsRef = useRef(null);\n\n  useEffect(() => {\n    const cards = cardsRef.current.children;\n    \n    gsap.fromTo(titleRef.current,\n      { y: 50, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 1,\n        scrollTrigger: {\n          trigger: titleRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    gsap.fromTo(cards,\n      { y: 80, opacity: 0, scale: 0.9 },\n      {\n        y: 0,\n        opacity: 1,\n        scale: 1,\n        duration: 0.8,\n        stagger: 0.2,\n        ease: \"power2.out\",\n        scrollTrigger: {\n          trigger: cardsRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    // Hover animations for cards\n    Array.from(cards).forEach(card => {\n      card.addEventListener('mouseenter', () => {\n        gsap.to(card, { y: -10, scale: 1.05, duration: 0.3, ease: \"power2.out\" });\n      });\n      \n      card.addEventListener('mouseleave', () => {\n        gsap.to(card, { y: 0, scale: 1, duration: 0.3, ease: \"power2.out\" });\n      });\n    });\n  }, []);\n\n  const features = [\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n        </svg>\n      ),\n      title: \"Lightning Fast\",\n      description: \"Process hours of audio in minutes with our optimized AI algorithms and cloud infrastructure.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n        </svg>\n      ),\n      title: \"99.5% Accuracy\",\n      description: \"Industry-leading accuracy with advanced noise reduction and context-aware processing.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129\" />\n        </svg>\n      ),\n      title: \"100+ Languages\",\n      description: \"Support for over 100 languages and dialects with automatic language detection.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n        </svg>\n      ),\n      title: \"Secure & Private\",\n      description: \"End-to-end encryption with local processing options. Your data never leaves your device.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n        </svg>\n      ),\n      title: \"Easy to Use\",\n      description: \"Intuitive interface with drag-and-drop functionality. No technical expertise required.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n        </svg>\n      ),\n      title: \"Multiple Formats\",\n      description: \"Support for all major audio formats including MP3, WAV, FLAC, M4A, and more.\"\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-white dark:bg-slate-900\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div ref={titleRef} className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Powerful Features</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto\">\n            Experience the next generation of audio transcription with cutting-edge AI technology\n            designed for accuracy, speed, and ease of use.\n          </p>\n        </div>\n\n        <div ref={cardsRef} className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <div\n              key={index}\n              className=\"group bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-200 dark:border-slate-700 cursor-pointer\"\n            >\n              <div className=\"text-blue-600 dark:text-blue-400 mb-6 group-hover:scale-110 transition-transform duration-300\">\n                {feature.icon}\n              </div>\n              <h3 className=\"text-2xl font-bold mb-4 text-slate-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300\">\n                {feature.title}\n              </h3>\n              <p className=\"text-slate-600 dark:text-slate-400 leading-relaxed\">\n                {feature.description}\n              </p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAElB,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,SAAS,OAAO,CAAC,QAAQ;QAEvC,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SAAS,OAAO,EAC1B;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YACE,GAAG;YACH,SAAS;YACT,UAAU;YACV,eAAe;gBACb,SAAS,SAAS,OAAO;gBACzB,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;QAGF,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OACV;YAAE,GAAG;YAAI,SAAS;YAAG,OAAO;QAAI,GAChC;YACE,GAAG;YACH,SAAS;YACT,OAAO;YACP,UAAU;YACV,SAAS;YACT,MAAM;YACN,eAAe;gBACb,SAAS,SAAS,OAAO;gBACzB,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;QAGF,6BAA6B;QAC7B,MAAM,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;YACxB,KAAK,gBAAgB,CAAC,cAAc;gBAClC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oBAAE,GAAG,CAAC;oBAAI,OAAO;oBAAM,UAAU;oBAAK,MAAM;gBAAa;YACzE;YAEA,KAAK,gBAAgB,CAAC,cAAc;gBAClC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oBAAE,GAAG;oBAAG,OAAO;oBAAG,UAAU;oBAAK,MAAM;gBAAa;YACpE;QACF;IACF,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,KAAK;oBAAU,WAAU;;sCAC5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAA+D;;;;;;;;;;;;8BAM9E,8OAAC;oBAAI,KAAK;oBAAU,WAAU;8BAC3B,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;8CAEf,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAVjB;;;;;;;;;;;;;;;;;;;;;AAkBnB", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/components/LanguagesSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function LanguagesSection() {\n  const sectionRef = useRef(null);\n  const titleRef = useRef(null);\n  const languagesRef = useRef(null);\n  const globeRef = useRef(null);\n\n  useEffect(() => {\n    const languages = languagesRef.current.children;\n    \n    gsap.fromTo(titleRef.current,\n      { y: 50, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 1,\n        scrollTrigger: {\n          trigger: titleRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    gsap.fromTo(globeRef.current,\n      { scale: 0.8, opacity: 0, rotation: -180 },\n      {\n        scale: 1,\n        opacity: 1,\n        rotation: 0,\n        duration: 1.5,\n        ease: \"power2.out\",\n        scrollTrigger: {\n          trigger: globeRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    gsap.fromTo(languages,\n      { y: 30, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 0.6,\n        stagger: 0.1,\n        ease: \"power2.out\",\n        scrollTrigger: {\n          trigger: languagesRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    // Continuous rotation for globe\n    gsap.to(globeRef.current, {\n      rotation: 360,\n      duration: 20,\n      ease: \"none\",\n      repeat: -1\n    });\n  }, []);\n\n  const languages = [\n    { name: \"English\", flag: \"🇺🇸\", accuracy: \"99.8%\" },\n    { name: \"Spanish\", flag: \"🇪🇸\", accuracy: \"99.5%\" },\n    { name: \"French\", flag: \"🇫🇷\", accuracy: \"99.3%\" },\n    { name: \"German\", flag: \"🇩🇪\", accuracy: \"99.4%\" },\n    { name: \"Italian\", flag: \"🇮🇹\", accuracy: \"99.2%\" },\n    { name: \"Portuguese\", flag: \"🇵🇹\", accuracy: \"99.1%\" },\n    { name: \"Russian\", flag: \"🇷🇺\", accuracy: \"98.9%\" },\n    { name: \"Chinese\", flag: \"🇨🇳\", accuracy: \"99.0%\" },\n    { name: \"Japanese\", flag: \"🇯🇵\", accuracy: \"98.8%\" },\n    { name: \"Korean\", flag: \"🇰🇷\", accuracy: \"98.7%\" },\n    { name: \"Arabic\", flag: \"🇸🇦\", accuracy: \"98.5%\" },\n    { name: \"Hindi\", flag: \"🇮🇳\", accuracy: \"98.6%\" },\n    { name: \"Dutch\", flag: \"🇳🇱\", accuracy: \"99.1%\" },\n    { name: \"Swedish\", flag: \"🇸🇪\", accuracy: \"99.0%\" },\n    { name: \"Norwegian\", flag: \"🇳🇴\", accuracy: \"98.9%\" },\n    { name: \"Danish\", flag: \"🇩🇰\", accuracy: \"98.8%\" },\n    { name: \"Finnish\", flag: \"🇫🇮\", accuracy: \"98.7%\" },\n    { name: \"Polish\", flag: \"🇵🇱\", accuracy: \"98.9%\" },\n    { name: \"Turkish\", flag: \"🇹🇷\", accuracy: \"98.6%\" },\n    { name: \"Greek\", flag: \"🇬🇷\", accuracy: \"98.5%\" },\n    { name: \"Hebrew\", flag: \"🇮🇱\", accuracy: \"98.4%\" },\n    { name: \"Thai\", flag: \"🇹🇭\", accuracy: \"98.3%\" },\n    { name: \"Vietnamese\", flag: \"🇻🇳\", accuracy: \"98.2%\" },\n    { name: \"Indonesian\", flag: \"🇮🇩\", accuracy: \"98.4%\" }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-slate-800 dark:via-slate-900 dark:to-purple-900\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div ref={titleRef} className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Global Language Support</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto\">\n            Break down language barriers with our comprehensive support for over 100 languages\n            and dialects, powered by advanced AI models trained on diverse datasets.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Globe Visualization */}\n          <div className=\"flex justify-center\">\n            <div ref={globeRef} className=\"relative w-80 h-80\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-20 blur-xl\"></div>\n              <div className=\"absolute inset-4 bg-gradient-to-br from-blue-500 to-purple-700 rounded-full flex items-center justify-center\">\n                <svg className=\"w-48 h-48 text-white opacity-80\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM11 19.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"/>\n                </svg>\n              </div>\n              <div className=\"absolute inset-0 border-4 border-blue-300 dark:border-blue-600 rounded-full animate-pulse\"></div>\n            </div>\n          </div>\n\n          {/* Languages Grid */}\n          <div ref={languagesRef} className=\"grid grid-cols-2 sm:grid-cols-3 gap-4\">\n            {languages.map((language, index) => (\n              <div\n                key={index}\n                className=\"group bg-white dark:bg-slate-800 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"text-3xl mb-2 group-hover:scale-110 transition-transform duration-300\">\n                    {language.flag}\n                  </div>\n                  <h3 className=\"font-semibold text-slate-800 dark:text-white mb-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300\">\n                    {language.name}\n                  </h3>\n                  <p className=\"text-sm text-green-600 dark:text-green-400 font-medium\">\n                    {language.accuracy}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mt-16 grid grid-cols-1 md:grid-cols-4 gap-8 text-center\">\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">100+</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Languages</div>\n          </div>\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">50+</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Dialects</div>\n          </div>\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">Auto</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Detection</div>\n          </div>\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">Real-time</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Processing</div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAElB,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO,CAAC,QAAQ;QAE/C,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SAAS,OAAO,EAC1B;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YACE,GAAG;YACH,SAAS;YACT,UAAU;YACV,eAAe;gBACb,SAAS,SAAS,OAAO;gBACzB,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;QAGF,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SAAS,OAAO,EAC1B;YAAE,OAAO;YAAK,SAAS;YAAG,UAAU,CAAC;QAAI,GACzC;YACE,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,MAAM;YACN,eAAe;gBACb,SAAS,SAAS,OAAO;gBACzB,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;QAGF,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,WACV;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YACE,GAAG;YACH,SAAS;YACT,UAAU;YACV,SAAS;YACT,MAAM;YACN,eAAe;gBACb,SAAS,aAAa,OAAO;gBAC7B,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;QAGF,gCAAgC;QAChC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS,OAAO,EAAE;YACxB,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ,CAAC;QACX;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAc,MAAM;YAAQ,UAAU;QAAQ;QACtD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAY,MAAM;YAAQ,UAAU;QAAQ;QACpD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAS,MAAM;YAAQ,UAAU;QAAQ;QACjD;YAAE,MAAM;YAAS,MAAM;YAAQ,UAAU;QAAQ;QACjD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAa,MAAM;YAAQ,UAAU;QAAQ;QACrD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAS,MAAM;YAAQ,UAAU;QAAQ;QACjD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAQ,MAAM;YAAQ,UAAU;QAAQ;QAChD;YAAE,MAAM;YAAc,MAAM;YAAQ,UAAU;QAAQ;QACtD;YAAE,MAAM;YAAc,MAAM;YAAQ,UAAU;QAAQ;KACvD;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,KAAK;oBAAU,WAAU;;sCAC5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAA+D;;;;;;;;;;;;8BAM9E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,KAAK;gCAAU,WAAU;;kDAC5B,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAkC,MAAK;4CAAe,SAAQ;sDAC3E,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAKnB,8OAAC;4BAAI,KAAK;4BAAc,WAAU;sCAC/B,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;oCAEC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,SAAS,IAAI;;;;;;0DAEhB,8OAAC;gDAAG,WAAU;0DACX,SAAS,IAAI;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,SAAS,QAAQ;;;;;;;;;;;;mCAXjB;;;;;;;;;;;;;;;;8BAoBb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhE", "debugId": null}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/components/HowItWorksSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function HowItWorksSection() {\n  const sectionRef = useRef(null);\n  const titleRef = useRef(null);\n  const stepsRef = useRef(null);\n  const connectorsRef = useRef(null);\n\n  useEffect(() => {\n    const steps = stepsRef.current.children;\n    const connectors = connectorsRef.current.children;\n    \n    gsap.fromTo(titleRef.current,\n      { y: 50, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 1,\n        scrollTrigger: {\n          trigger: titleRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    // Animate steps sequentially\n    gsap.fromTo(steps,\n      { y: 80, opacity: 0, scale: 0.8 },\n      {\n        y: 0,\n        opacity: 1,\n        scale: 1,\n        duration: 0.8,\n        stagger: 0.3,\n        ease: \"power2.out\",\n        scrollTrigger: {\n          trigger: stepsRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    // Animate connectors after steps\n    gsap.fromTo(connectors,\n      { scaleX: 0, opacity: 0 },\n      {\n        scaleX: 1,\n        opacity: 1,\n        duration: 0.6,\n        stagger: 0.3,\n        delay: 0.5,\n        ease: \"power2.out\",\n        scrollTrigger: {\n          trigger: stepsRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    // Pulse animation for step numbers\n    Array.from(steps).forEach((step, index) => {\n      const numberElement = step.querySelector('.step-number');\n      gsap.to(numberElement, {\n        scale: 1.1,\n        duration: 1,\n        ease: \"power2.inOut\",\n        yoyo: true,\n        repeat: -1,\n        delay: index * 0.5\n      });\n    });\n  }, []);\n\n  const steps = [\n    {\n      number: \"01\",\n      title: \"Upload Your Audio\",\n      description: \"Simply drag and drop your audio file or click to browse. We support all major formats including MP3, WAV, FLAC, M4A, and more.\",\n      icon: (\n        <svg className=\"w-16 h-16\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n        </svg>\n      )\n    },\n    {\n      number: \"02\",\n      title: \"AI Processing\",\n      description: \"Our advanced AI algorithms analyze your audio, detect the language automatically, and begin the transcription process with noise reduction.\",\n      icon: (\n        <svg className=\"w-16 h-16\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n        </svg>\n      )\n    },\n    {\n      number: \"03\",\n      title: \"Smart Transcription\",\n      description: \"Advanced speech recognition converts your audio to text with context-aware processing, punctuation, and speaker identification.\",\n      icon: (\n        <svg className=\"w-16 h-16\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n        </svg>\n      )\n    },\n    {\n      number: \"04\",\n      title: \"Download Results\",\n      description: \"Get your accurate transcription in multiple formats including TXT, DOCX, SRT, and VTT. Edit and export as needed.\",\n      icon: (\n        <svg className=\"w-16 h-16\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-white dark:bg-slate-900\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div ref={titleRef} className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">How It Works</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto\">\n            Transform your audio to text in just four simple steps. Our streamlined process\n            ensures accuracy and efficiency every time.\n          </p>\n        </div>\n\n        <div className=\"relative\">\n          {/* Steps */}\n          <div ref={stepsRef} className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 relative z-10\">\n            {steps.map((step, index) => (\n              <div\n                key={index}\n                className=\"group text-center\"\n              >\n                {/* Step Card */}\n                <div className=\"bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 relative overflow-hidden\">\n                  {/* Background Gradient */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n                  \n                  {/* Step Number */}\n                  <div className=\"step-number absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-xl rounded-full flex items-center justify-center shadow-lg\">\n                    {step.number}\n                  </div>\n                  \n                  {/* Icon */}\n                  <div className=\"text-blue-600 dark:text-blue-400 mb-6 group-hover:scale-110 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-all duration-300 relative z-10\">\n                    {step.icon}\n                  </div>\n                  \n                  {/* Content */}\n                  <div className=\"relative z-10\">\n                    <h3 className=\"text-2xl font-bold mb-4 text-slate-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300\">\n                      {step.title}\n                    </h3>\n                    <p className=\"text-slate-600 dark:text-slate-400 leading-relaxed\">\n                      {step.description}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Connectors */}\n          <div ref={connectorsRef} className=\"hidden lg:block absolute top-1/2 left-0 right-0 z-0\">\n            <div className=\"flex justify-between items-center px-32\">\n              {[1, 2, 3].map((_, index) => (\n                <div\n                  key={index}\n                  className=\"w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full relative\"\n                >\n                  <div className=\"absolute -right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-purple-500 rounded-full\"></div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* CTA */}\n        <div className=\"text-center mt-16\">\n          <button className=\"btn-hover-effect bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300\">\n            Try It Now - It's Free!\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAElB,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,SAAS,OAAO,CAAC,QAAQ;QACvC,MAAM,aAAa,cAAc,OAAO,CAAC,QAAQ;QAEjD,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SAAS,OAAO,EAC1B;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YACE,GAAG;YACH,SAAS;YACT,UAAU;YACV,eAAe;gBACb,SAAS,SAAS,OAAO;gBACzB,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;QAGF,6BAA6B;QAC7B,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OACV;YAAE,GAAG;YAAI,SAAS;YAAG,OAAO;QAAI,GAChC;YACE,GAAG;YACH,SAAS;YACT,OAAO;YACP,UAAU;YACV,SAAS;YACT,MAAM;YACN,eAAe;gBACb,SAAS,SAAS,OAAO;gBACzB,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;QAGF,iCAAiC;QACjC,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,YACV;YAAE,QAAQ;YAAG,SAAS;QAAE,GACxB;YACE,QAAQ;YACR,SAAS;YACT,UAAU;YACV,SAAS;YACT,OAAO;YACP,MAAM;YACN,eAAe;gBACb,SAAS,SAAS,OAAO;gBACzB,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;QAGF,mCAAmC;QACnC,MAAM,IAAI,CAAC,OAAO,OAAO,CAAC,CAAC,MAAM;YAC/B,MAAM,gBAAgB,KAAK,aAAa,CAAC;YACzC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,eAAe;gBACrB,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,MAAM;gBACN,QAAQ,CAAC;gBACT,OAAO,QAAQ;YACjB;QACF;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,KAAK;oBAAU,WAAU;;sCAC5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAA+D;;;;;;;;;;;;8BAM9E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,KAAK;4BAAU,WAAU;sCAC3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAEC,WAAU;8CAGV,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC;gDAAI,WAAU;0DACZ,KAAK,MAAM;;;;;;0DAId,8OAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDAAE,WAAU;kEACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;mCAxBlB;;;;;;;;;;sCAiCX,8OAAC;4BAAI,KAAK;4BAAe,WAAU;sCACjC,cAAA,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,sBACjB,8OAAC;wCAEC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;;;;;uCAHV;;;;;;;;;;;;;;;;;;;;;8BAWf,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAO,WAAU;kCAAmP;;;;;;;;;;;;;;;;;;;;;;AAO/Q", "debugId": null}}, {"offset": {"line": 1536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/components/UseCasesSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function UseCasesSection() {\n  const sectionRef = useRef(null);\n  const titleRef = useRef(null);\n  const tabsRef = useRef(null);\n  const contentRef = useRef(null);\n  const [activeTab, setActiveTab] = useState(0);\n\n  useEffect(() => {\n    gsap.fromTo(titleRef.current,\n      { y: 50, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 1,\n        scrollTrigger: {\n          trigger: titleRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    gsap.fromTo(tabsRef.current.children,\n      { y: 30, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 0.6,\n        stagger: 0.1,\n        scrollTrigger: {\n          trigger: tabsRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n  }, []);\n\n  useEffect(() => {\n    // Animate content change\n    gsap.fromTo(contentRef.current,\n      { opacity: 0, x: 20 },\n      { opacity: 1, x: 0, duration: 0.5, ease: \"power2.out\" }\n    );\n  }, [activeTab]);\n\n  const useCases = [\n    {\n      title: \"Business & Meetings\",\n      icon: \"💼\",\n      description: \"Transform meeting recordings, conference calls, and business presentations into searchable text documents.\",\n      features: [\n        \"Meeting transcriptions with speaker identification\",\n        \"Conference call summaries and action items\",\n        \"Presentation notes and key points extraction\",\n        \"Client call documentation\"\n      ],\n      image: \"📊\"\n    },\n    {\n      title: \"Education & Learning\",\n      icon: \"🎓\",\n      description: \"Convert lectures, seminars, and educational content into study materials and accessible text formats.\",\n      features: [\n        \"Lecture transcriptions for note-taking\",\n        \"Online course subtitle generation\",\n        \"Research interview documentation\",\n        \"Student accessibility support\"\n      ],\n      image: \"📚\"\n    },\n    {\n      title: \"Content Creation\",\n      icon: \"🎬\",\n      description: \"Generate subtitles, captions, and transcripts for videos, podcasts, and multimedia content.\",\n      features: [\n        \"YouTube video subtitle creation\",\n        \"Podcast episode transcriptions\",\n        \"Social media content captions\",\n        \"Video accessibility compliance\"\n      ],\n      image: \"🎥\"\n    },\n    {\n      title: \"Legal & Medical\",\n      icon: \"⚖️\",\n      description: \"Accurate transcription of legal proceedings, medical consultations, and professional documentation.\",\n      features: [\n        \"Court proceeding documentation\",\n        \"Medical consultation records\",\n        \"Legal interview transcriptions\",\n        \"Compliance and audit trails\"\n      ],\n      image: \"🏥\"\n    },\n    {\n      title: \"Journalism & Media\",\n      icon: \"📰\",\n      description: \"Convert interviews, press conferences, and media content into written articles and reports.\",\n      features: [\n        \"Interview transcription and quotes\",\n        \"Press conference documentation\",\n        \"News story research and fact-checking\",\n        \"Media archive digitization\"\n      ],\n      image: \"🎤\"\n    },\n    {\n      title: \"Personal Use\",\n      icon: \"👤\",\n      description: \"Transcribe personal recordings, voice memos, and family memories into text for easy organization.\",\n      features: [\n        \"Voice memo transcription\",\n        \"Family story preservation\",\n        \"Personal journal conversion\",\n        \"Travel log documentation\"\n      ],\n      image: \"📝\"\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-slate-800 dark:via-slate-900 dark:to-purple-900\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div ref={titleRef} className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Perfect for Every Use Case</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto\">\n            From business meetings to personal projects, our AI transcription tool adapts to your needs\n            with industry-specific accuracy and formatting.\n          </p>\n        </div>\n\n        {/* Tabs */}\n        <div ref={tabsRef} className=\"flex flex-wrap justify-center gap-4 mb-12\">\n          {useCases.map((useCase, index) => (\n            <button\n              key={index}\n              onClick={() => setActiveTab(index)}\n              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 flex items-center gap-2 ${\n                activeTab === index\n                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg transform scale-105'\n                  : 'bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700 shadow-md hover:shadow-lg'\n              }`}\n            >\n              <span className=\"text-xl\">{useCase.icon}</span>\n              <span className=\"hidden sm:inline\">{useCase.title}</span>\n            </button>\n          ))}\n        </div>\n\n        {/* Content */}\n        <div ref={contentRef} className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Text Content */}\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center gap-4 mb-6\">\n              <div className=\"text-6xl\">{useCases[activeTab].icon}</div>\n              <h3 className=\"text-3xl font-bold text-slate-800 dark:text-white\">\n                {useCases[activeTab].title}\n              </h3>\n            </div>\n            \n            <p className=\"text-lg text-slate-600 dark:text-slate-400 leading-relaxed\">\n              {useCases[activeTab].description}\n            </p>\n\n            <div className=\"space-y-3\">\n              <h4 className=\"text-xl font-semibold text-slate-800 dark:text-white mb-4\">\n                Key Features:\n              </h4>\n              {useCases[activeTab].features.map((feature, index) => (\n                <div key={index} className=\"flex items-center gap-3\">\n                  <div className=\"w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full\"></div>\n                  <span className=\"text-slate-600 dark:text-slate-400\">{feature}</span>\n                </div>\n              ))}\n            </div>\n\n            <button className=\"btn-hover-effect bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 mt-6\">\n              Try This Use Case\n            </button>\n          </div>\n\n          {/* Visual Content */}\n          <div className=\"flex justify-center\">\n            <div className=\"relative w-80 h-80 bg-gradient-to-br from-white to-slate-100 dark:from-slate-800 dark:to-slate-900 rounded-3xl shadow-2xl flex items-center justify-center border border-slate-200 dark:border-slate-700\">\n              <div className=\"text-9xl opacity-80\">\n                {useCases[activeTab].image}\n              </div>\n              \n              {/* Floating Elements */}\n              <div className=\"absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white text-2xl shadow-lg animate-bounce\">\n                ✨\n              </div>\n              \n              <div className=\"absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center text-white text-lg shadow-lg animate-pulse\">\n                🚀\n              </div>\n              \n              <div className=\"absolute top-1/4 -left-6 w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white text-sm shadow-lg animate-ping\">\n                ⚡\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mt-16 grid grid-cols-2 md:grid-cols-4 gap-6 text-center\">\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-2xl font-bold gradient-text mb-2\">1M+</div>\n            <div className=\"text-slate-600 dark:text-slate-400 text-sm\">Hours Transcribed</div>\n          </div>\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-2xl font-bold gradient-text mb-2\">50K+</div>\n            <div className=\"text-slate-600 dark:text-slate-400 text-sm\">Happy Users</div>\n          </div>\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-2xl font-bold gradient-text mb-2\">99.5%</div>\n            <div className=\"text-slate-600 dark:text-slate-400 text-sm\">Accuracy Rate</div>\n          </div>\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-2xl font-bold gradient-text mb-2\">24/7</div>\n            <div className=\"text-slate-600 dark:text-slate-400 text-sm\">Support</div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAElB,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SAAS,OAAO,EAC1B;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YACE,GAAG;YACH,SAAS;YACT,UAAU;YACV,eAAe;gBACb,SAAS,SAAS,OAAO;gBACzB,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;QAGF,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,QAAQ,OAAO,CAAC,QAAQ,EAClC;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YACE,GAAG;YACH,SAAS;YACT,UAAU;YACV,SAAS;YACT,eAAe;gBACb,SAAS,QAAQ,OAAO;gBACxB,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;IAEJ,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yBAAyB;QACzB,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,WAAW,OAAO,EAC5B;YAAE,SAAS;YAAG,GAAG;QAAG,GACpB;YAAE,SAAS;YAAG,GAAG;YAAG,UAAU;YAAK,MAAM;QAAa;IAE1D,GAAG;QAAC;KAAU;IAEd,MAAM,WAAW;QACf;YACE,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,KAAK;oBAAU,WAAU;;sCAC5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAA+D;;;;;;;;;;;;8BAO9E,8OAAC;oBAAI,KAAK;oBAAS,WAAU;8BAC1B,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,uFAAuF,EACjG,cAAc,QACV,0FACA,sIACJ;;8CAEF,8OAAC;oCAAK,WAAU;8CAAW,QAAQ,IAAI;;;;;;8CACvC,8OAAC;oCAAK,WAAU;8CAAoB,QAAQ,KAAK;;;;;;;2BAT5C;;;;;;;;;;8BAeX,8OAAC;oBAAI,KAAK;oBAAY,WAAU;;sCAE9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAY,QAAQ,CAAC,UAAU,CAAC,IAAI;;;;;;sDACnD,8OAAC;4CAAG,WAAU;sDACX,QAAQ,CAAC,UAAU,CAAC,KAAK;;;;;;;;;;;;8CAI9B,8OAAC;oCAAE,WAAU;8CACV,QAAQ,CAAC,UAAU,CAAC,WAAW;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA4D;;;;;;wCAGzE,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC1C,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAsC;;;;;;;+CAF9C;;;;;;;;;;;8CAOd,8OAAC;oCAAO,WAAU;8CAAgP;;;;;;;;;;;;sCAMpQ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,CAAC,UAAU,CAAC,KAAK;;;;;;kDAI5B,8OAAC;wCAAI,WAAU;kDAA6K;;;;;;kDAI5L,8OAAC;wCAAI,WAAU;kDAA2K;;;;;;kDAI1L,8OAAC;wCAAI,WAAU;kDAAwK;;;;;;;;;;;;;;;;;;;;;;;8BAQ7L,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAE9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAE9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAE9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxE", "debugId": null}}, {"offset": {"line": 2021, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/components/CTASection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function CTASection() {\n  const sectionRef = useRef(null);\n  const contentRef = useRef(null);\n  const buttonsRef = useRef(null);\n  const backgroundRef = useRef(null);\n\n  useEffect(() => {\n    const tl = gsap.timeline({\n      scrollTrigger: {\n        trigger: sectionRef.current,\n        start: 'top 80%',\n        end: 'bottom 20%',\n        toggleActions: 'play none none reverse'\n      }\n    });\n\n    // Background animation\n    tl.fromTo(backgroundRef.current,\n      { scale: 1.1, opacity: 0 },\n      { scale: 1, opacity: 1, duration: 1.5, ease: \"power2.out\" }\n    );\n\n    // Content animation\n    tl.fromTo(contentRef.current,\n      { y: 80, opacity: 0 },\n      { y: 0, opacity: 1, duration: 1, ease: \"power3.out\" },\n      \"-=1\"\n    );\n\n    // Buttons animation\n    tl.fromTo(buttonsRef.current.children,\n      { y: 40, opacity: 0, scale: 0.9 },\n      { y: 0, opacity: 1, scale: 1, duration: 0.8, stagger: 0.2, ease: \"power2.out\" },\n      \"-=0.5\"\n    );\n\n    // Floating animation for background elements\n    gsap.to('.cta-floating', {\n      y: -30,\n      duration: 4,\n      ease: \"power1.inOut\",\n      yoyo: true,\n      repeat: -1,\n      stagger: 0.8\n    });\n\n    // Pulse animation for main CTA button\n    gsap.to('.pulse-button', {\n      scale: 1.05,\n      duration: 2,\n      ease: \"power1.inOut\",\n      yoyo: true,\n      repeat: -1\n    });\n  }, []);\n\n  return (\n    <section ref={sectionRef} className=\"relative py-20 overflow-hidden\">\n      {/* Background */}\n      <div ref={backgroundRef} className=\"absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800\">\n        <div className=\"absolute inset-0 bg-black opacity-20\"></div>\n        \n        {/* Floating Elements */}\n        <div className=\"cta-floating absolute top-20 left-10 w-32 h-32 bg-white opacity-10 rounded-full blur-xl\"></div>\n        <div className=\"cta-floating absolute top-40 right-20 w-24 h-24 bg-purple-300 opacity-15 rounded-full blur-lg\"></div>\n        <div className=\"cta-floating absolute bottom-32 left-1/4 w-40 h-40 bg-blue-300 opacity-10 rounded-full blur-2xl\"></div>\n        <div className=\"cta-floating absolute bottom-20 right-1/3 w-28 h-28 bg-white opacity-15 rounded-full blur-xl\"></div>\n        \n        {/* Grid Pattern */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"grid grid-cols-12 gap-4 h-full\">\n            {Array.from({ length: 48 }).map((_, i) => (\n              <div key={i} className=\"border border-white opacity-20\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-6xl mx-auto px-6 text-center text-white\">\n        <div ref={contentRef}>\n          <h2 className=\"text-4xl md:text-6xl font-bold mb-6 leading-tight\">\n            Ready to Transform Your\n            <br />\n            <span className=\"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n              Audio to Text?\n            </span>\n          </h2>\n          \n          <p className=\"text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed\">\n            Join thousands of professionals who trust our AI-powered transcription service.\n            Start your free trial today and experience the future of audio transcription.\n          </p>\n\n          {/* Features List */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto\">\n            <div className=\"flex items-center justify-center gap-3 bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-4\">\n              <svg className=\"w-6 h-6 text-green-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n              <span className=\"font-semibold\">Free 30-minute trial</span>\n            </div>\n            <div className=\"flex items-center justify-center gap-3 bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-4\">\n              <svg className=\"w-6 h-6 text-green-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n              <span className=\"font-semibold\">No credit card required</span>\n            </div>\n            <div className=\"flex items-center justify-center gap-3 bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-4\">\n              <svg className=\"w-6 h-6 text-green-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n              <span className=\"font-semibold\">Cancel anytime</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Buttons */}\n        <div ref={buttonsRef} className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\">\n          <button className=\"pulse-button btn-hover-effect group bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-black px-10 py-5 rounded-2xl font-bold text-xl shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 transition-all duration-300 flex items-center gap-4 min-w-[280px]\">\n            <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\" clipRule=\"evenodd\" />\n            </svg>\n            Start Free Trial Now\n          </button>\n          \n          <button className=\"btn-hover-effect group bg-transparent border-2 border-white hover:bg-white hover:text-blue-600 text-white px-10 py-5 rounded-2xl font-bold text-xl shadow-lg hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300 flex items-center gap-4 min-w-[280px]\">\n            <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            Watch Demo Video\n          </button>\n        </div>\n\n        {/* Trust Indicators */}\n        <div className=\"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 opacity-80\">\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold mb-2\">50K+</div>\n            <div className=\"text-sm opacity-75\">Active Users</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold mb-2\">1M+</div>\n            <div className=\"text-sm opacity-75\">Hours Processed</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold mb-2\">99.5%</div>\n            <div className=\"text-sm opacity-75\">Accuracy Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold mb-2\">4.9★</div>\n            <div className=\"text-sm opacity-75\">User Rating</div>\n          </div>\n        </div>\n\n        {/* Urgency Element */}\n        <div className=\"mt-12 bg-red-500 bg-opacity-20 backdrop-blur-sm border border-red-300 border-opacity-30 rounded-xl p-6 max-w-2xl mx-auto\">\n          <div className=\"flex items-center justify-center gap-3 mb-3\">\n            <svg className=\"w-6 h-6 text-red-300 animate-pulse\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n            <span className=\"font-bold text-red-300\">Limited Time Offer</span>\n          </div>\n          <p className=\"text-white\">\n            Get 50% off your first month when you sign up in the next 24 hours!\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAElB,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACvB,eAAe;gBACb,SAAS,WAAW,OAAO;gBAC3B,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;QAEA,uBAAuB;QACvB,GAAG,MAAM,CAAC,cAAc,OAAO,EAC7B;YAAE,OAAO;YAAK,SAAS;QAAE,GACzB;YAAE,OAAO;YAAG,SAAS;YAAG,UAAU;YAAK,MAAM;QAAa;QAG5D,oBAAoB;QACpB,GAAG,MAAM,CAAC,WAAW,OAAO,EAC1B;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YAAE,GAAG;YAAG,SAAS;YAAG,UAAU;YAAG,MAAM;QAAa,GACpD;QAGF,oBAAoB;QACpB,GAAG,MAAM,CAAC,WAAW,OAAO,CAAC,QAAQ,EACnC;YAAE,GAAG;YAAI,SAAS;YAAG,OAAO;QAAI,GAChC;YAAE,GAAG;YAAG,SAAS;YAAG,OAAO;YAAG,UAAU;YAAK,SAAS;YAAK,MAAM;QAAa,GAC9E;QAGF,6CAA6C;QAC7C,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,iBAAiB;YACvB,GAAG,CAAC;YACJ,UAAU;YACV,MAAM;YACN,MAAM;YACN,QAAQ,CAAC;YACT,SAAS;QACX;QAEA,sCAAsC;QACtC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,iBAAiB;YACvB,OAAO;YACP,UAAU;YACV,MAAM;YACN,MAAM;YACN,QAAQ,CAAC;QACX;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,8OAAC;gBAAI,KAAK;gBAAe,WAAU;;kCACjC,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC;oCAAY,WAAU;mCAAb;;;;;;;;;;;;;;;;;;;;;0BAOlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,KAAK;;0CACR,8OAAC;gCAAG,WAAU;;oCAAoD;kDAEhE,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAA+E;;;;;;;;;;;;0CAKjG,8OAAC;gCAAE,WAAU;0CAAwE;;;;;;0CAMrF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAe,SAAQ;0DAClE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;0DAE3J,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAe,SAAQ;0DAClE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;0DAE3J,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAe,SAAQ;0DAClE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;0DAE3J,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC;wBAAI,KAAK;wBAAY,WAAU;;0CAC9B,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA0G,UAAS;;;;;;;;;;;oCAC1I;;;;;;;0CAIR,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;kCAMV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;;;;;;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;;;;;;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;;;;;;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;;;;;;;;;;;;;kCAKxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAqC,MAAK;wCAAe,SAAQ;kDAC9E,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAoN,UAAS;;;;;;;;;;;kDAE1P,8OAAC;wCAAK,WAAU;kDAAyB;;;;;;;;;;;;0CAE3C,8OAAC;gCAAE,WAAU;0CAAa;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 2574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/components/FAQSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function FAQSection() {\n  const sectionRef = useRef(null);\n  const titleRef = useRef(null);\n  const faqsRef = useRef(null);\n  const [openFAQ, setOpenFAQ] = useState(0);\n\n  useEffect(() => {\n    gsap.fromTo(titleRef.current,\n      { y: 50, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 1,\n        scrollTrigger: {\n          trigger: titleRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    gsap.fromTo(faqsRef.current.children,\n      { y: 30, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 0.6,\n        stagger: 0.1,\n        scrollTrigger: {\n          trigger: faqsRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n  }, []);\n\n  const faqs = [\n    {\n      question: \"How accurate is the AI transcription?\",\n      answer: \"Our AI achieves 99.5% accuracy on clear audio recordings. Accuracy may vary based on audio quality, background noise, accents, and technical terminology. We continuously improve our models with regular updates and training on diverse datasets.\"\n    },\n    {\n      question: \"What audio formats do you support?\",\n      answer: \"We support all major audio formats including MP3, WAV, FLAC, M4A, AAC, OGG, WMA, and more. You can also upload video files (MP4, AVI, MOV, MKV) and we'll extract the audio automatically for transcription.\"\n    },\n    {\n      question: \"How long does transcription take?\",\n      answer: \"Our AI processes audio at 10x real-time speed. A 1-hour audio file typically takes 6-10 minutes to transcribe, depending on audio quality and current server load. You'll receive real-time progress updates during processing.\"\n    },\n    {\n      question: \"Is my data secure and private?\",\n      answer: \"Absolutely. We use end-to-end encryption for all uploads and processing. Your files are automatically deleted from our servers after 24 hours. We offer local processing options for sensitive content and are fully GDPR and CCPA compliant.\"\n    },\n    {\n      question: \"Can you identify different speakers?\",\n      answer: \"Yes! Our speaker diarization feature can identify and separate different speakers in your audio. This is perfect for meetings, interviews, and multi-person conversations. Each speaker is labeled clearly in the transcript.\"\n    },\n    {\n      question: \"What languages do you support?\",\n      answer: \"We support over 100 languages and dialects including English, Spanish, French, German, Italian, Portuguese, Russian, Chinese, Japanese, Korean, Arabic, Hindi, and many more. Our AI automatically detects the language or you can specify it manually.\"\n    },\n    {\n      question: \"Do you offer a free trial?\",\n      answer: \"Yes! We offer a free trial that includes 30 minutes of transcription time. No credit card required to start. You can test our accuracy and features before committing to a paid plan.\"\n    },\n    {\n      question: \"Can I edit the transcription?\",\n      answer: \"Absolutely! Our built-in editor allows you to make corrections, add timestamps, format text, and export in multiple formats (TXT, DOCX, SRT, VTT). You can also collaborate with team members on transcript editing.\"\n    },\n    {\n      question: \"What's the maximum file size?\",\n      answer: \"Free accounts can upload files up to 100MB. Paid plans support files up to 2GB. For larger files, you can split them or contact our support team for enterprise solutions that handle files of any size.\"\n    },\n    {\n      question: \"Do you offer API access?\",\n      answer: \"Yes! We provide a robust REST API for developers who want to integrate transcription into their applications. Our API supports real-time streaming, batch processing, and webhook notifications for completed transcriptions.\"\n    }\n  ];\n\n  const toggleFAQ = (index) => {\n    setOpenFAQ(openFAQ === index ? -1 : index);\n  };\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-white dark:bg-slate-900\">\n      <div className=\"max-w-4xl mx-auto px-6\">\n        <div ref={titleRef} className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Frequently Asked Questions</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto\">\n            Got questions? We've got answers. Find everything you need to know about our\n            AI transcription service below.\n          </p>\n        </div>\n\n        <div ref={faqsRef} className=\"space-y-4\">\n          {faqs.map((faq, index) => (\n            <div\n              key={index}\n              className=\"bg-gradient-to-br from-slate-50 to-white dark:from-slate-800 dark:to-slate-900 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 overflow-hidden\"\n            >\n              <button\n                onClick={() => toggleFAQ(index)}\n                className=\"w-full px-8 py-6 text-left flex items-center justify-between hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors duration-300 group\"\n              >\n                <h3 className=\"text-lg font-semibold text-slate-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300 pr-4\">\n                  {faq.question}\n                </h3>\n                <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white transition-transform duration-300 ${openFAQ === index ? 'rotate-180' : ''}`}>\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </div>\n              </button>\n              \n              <div className={`overflow-hidden transition-all duration-500 ease-in-out ${openFAQ === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>\n                <div className=\"px-8 pb-6\">\n                  <div className=\"w-full h-px bg-gradient-to-r from-blue-200 to-purple-200 dark:from-blue-800 dark:to-purple-800 mb-4\"></div>\n                  <p className=\"text-slate-600 dark:text-slate-400 leading-relaxed\">\n                    {faq.answer}\n                  </p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Contact Support */}\n        <div className=\"mt-16 text-center bg-gradient-to-r from-blue-50 to-purple-50 dark:from-slate-800 dark:to-slate-900 rounded-2xl p-8 border border-slate-200 dark:border-slate-700\">\n          <h3 className=\"text-2xl font-bold mb-4 text-slate-800 dark:text-white\">\n            Still have questions?\n          </h3>\n          <p className=\"text-slate-600 dark:text-slate-400 mb-6\">\n            Our support team is here to help you 24/7. Get in touch and we'll respond within minutes.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"btn-hover-effect bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center justify-center gap-2\">\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n              </svg>\n              Live Chat Support\n            </button>\n            <button className=\"btn-hover-effect bg-white dark:bg-slate-800 border-2 border-slate-300 dark:border-slate-600 hover:border-blue-300 dark:hover:border-blue-600 text-slate-700 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center justify-center gap-2\">\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n              </svg>\n              Email Support\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAElB,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SAAS,OAAO,EAC1B;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YACE,GAAG;YACH,SAAS;YACT,UAAU;YACV,eAAe;gBACb,SAAS,SAAS,OAAO;gBACzB,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;QAGF,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,QAAQ,OAAO,CAAC,QAAQ,EAClC;YAAE,GAAG;YAAI,SAAS;QAAE,GACpB;YACE,GAAG;YACH,SAAS;YACT,UAAU;YACV,SAAS;YACT,eAAe;gBACb,SAAS,QAAQ,OAAO;gBACxB,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;IAEJ,GAAG,EAAE;IAEL,MAAM,OAAO;QACX;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;KACD;IAED,MAAM,YAAY,CAAC;QACjB,WAAW,YAAY,QAAQ,CAAC,IAAI;IACtC;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,KAAK;oBAAU,WAAU;;sCAC5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAA+D;;;;;;;;;;;;8BAM9E,8OAAC;oBAAI,KAAK;oBAAS,WAAU;8BAC1B,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;sDACX,IAAI,QAAQ;;;;;;sDAEf,8OAAC;4CAAI,WAAW,CAAC,8JAA8J,EAAE,YAAY,QAAQ,eAAe,IAAI;sDACtN,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAK3E,8OAAC;oCAAI,WAAW,CAAC,wDAAwD,EAAE,YAAY,QAAQ,yBAAyB,qBAAqB;8CAC3I,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DACV,IAAI,MAAM;;;;;;;;;;;;;;;;;;2BArBZ;;;;;;;;;;8BA8BX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;sCAGvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;8CAGR,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}, {"offset": {"line": 2910, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport HeroSection from './components/HeroSection';\nimport FeaturesSection from './components/FeaturesSection';\nimport LanguagesSection from './components/LanguagesSection';\nimport HowItWorksSection from './components/HowItWorksSection';\nimport UseCasesSection from './components/UseCasesSection';\nimport CTASection from './components/CTASection';\nimport FAQSection from './components/FAQSection';\nimport Footer from './components/Footer';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function Home() {\n  useEffect(() => {\n    // Initialize GSAP animations\n    gsap.fromTo('.fade-in',\n      { opacity: 0, y: 50 },\n      {\n        opacity: 1,\n        y: 0,\n        duration: 1,\n        stagger: 0.2,\n        scrollTrigger: {\n          trigger: '.fade-in',\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n  }, []);\n\n  return (\n    <main className=\"min-h-screen\">\n      <HeroSection />\n      <FeaturesSection />\n      <LanguagesSection />\n      <HowItWorksSection />\n      <UseCasesSection />\n      <CTASection />\n      <FAQSection />\n      <Footer />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAXA;;;;;;;;;;;;;AAcA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAElB,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,YACV;YAAE,SAAS;YAAG,GAAG;QAAG,GACpB;YACE,SAAS;YACT,GAAG;YACH,UAAU;YACV,SAAS;YACT,eAAe;gBACb,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,eAAe;YACjB;QACF;IAEJ,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,uIAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,2IAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,4IAAA,CAAA,UAAgB;;;;;0BACjB,8OAAC,6IAAA,CAAA,UAAiB;;;;;0BAClB,8OAAC,2IAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,sIAAA,CAAA,UAAU;;;;;0BACX,8OAAC,sIAAA,CAAA,UAAU;;;;;0BACX,8OAAC;;;;;;;;;;;AAGP", "debugId": null}}]}