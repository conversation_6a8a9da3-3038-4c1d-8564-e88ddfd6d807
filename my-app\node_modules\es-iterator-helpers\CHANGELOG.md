# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.1](https://github.com/es-shims/iterator-helpers/compare/v1.2.0...v1.2.1) - 2024-12-20

### Commits

- [Tests] `Iterator.concat`: add most of the tests from https://github.com/tc39/test262/pull/4326 [`2e340da`](https://github.com/es-shims/iterator-helpers/commit/2e340daf021139ea41dc828c7f99452114db8561)
- [Fix] `Iterator.concat`: rewrite implementation to match updated spec text [`1a70fd3`](https://github.com/es-shims/iterator-helpers/commit/1a70fd33ab81738ad2b748812a431d51e0c4d758)
- [Fix] `IteratorZip` AO: allow an empty list of iterators [`265e566`](https://github.com/es-shims/iterator-helpers/commit/265e5664dade59e2faa5d7d2e203b92cd0af5c35)
- [actions] split out node 10-20, and 20+ [`3cc9db0`](https://github.com/es-shims/iterator-helpers/commit/3cc9db03118e2b4b6432e262e7936a7e2f3074c4)
- [Fix] `IteratorHelperPrototype`: in pre-proto envs, use same `.return` impl [`da02ab6`](https://github.com/es-shims/iterator-helpers/commit/da02ab656dce1c4f73355290a3bd9e4c25ccc3b6)
- [Deps] update `call-bind`, `es-abstract`, `get-intrinsic`, `gopd`, `has-proto`, `internal-slot`, `iterator.prototype`, `safe-array-concat` [`e9eefab`](https://github.com/es-shims/iterator-helpers/commit/e9eefab99fb496f9a4600193126c69c321394d42)
- [Tests] `Iterator.zip`: nullish non-function Symbol.iterator does not throw [`c6b0705`](https://github.com/es-shims/iterator-helpers/commit/c6b0705e2b601c1c8d412679eca4fe4618051d8f)
- [Refactor] use `call-bound` directly [`92afc5a`](https://github.com/es-shims/iterator-helpers/commit/92afc5a7ed014b5e2b52db1b1a6a99d16f50d5d2)
- [Deps] update `gopd`, `has-proto`, `has-symbols` [`7f8a1c0`](https://github.com/es-shims/iterator-helpers/commit/7f8a1c0049dfd649bea9a707b9c451ba7e9f3b0e)
- [readme] add missing info for `Iterator.zip`, `Iterator.zipKeyed` [`dd80b2f`](https://github.com/es-shims/iterator-helpers/commit/dd80b2f1718092219e94e24afdc1737f062e3df4)
- [Dev Deps] update `@es-shims/api` [`1e9857c`](https://github.com/es-shims/iterator-helpers/commit/1e9857c497f8f4e6053c9adb7dddb98b6800c628)
- [Deps] update `es-abstract` [`c8157da`](https://github.com/es-shims/iterator-helpers/commit/c8157da5194372ddc968e0f512d0740544752d30)
- [Deps] update `es-abstract` [`256de0e`](https://github.com/es-shims/iterator-helpers/commit/256de0e1aa45d6d0ad12a19ef04491816bcb7288)
- [Dev Deps] update `object-inspect` [`2f8544b`](https://github.com/es-shims/iterator-helpers/commit/2f8544b7baa80174e7368d475a4e3fa577418938)

## [v1.2.0](https://github.com/es-shims/iterator-helpers/compare/v1.1.0...v1.2.0) - 2024-11-04

### Commits

- [New] add `Iterator.zip`, `Iterator.zipKeyed` [`d11073f`](https://github.com/es-shims/iterator-helpers/commit/d11073fd27fcdaf696c3d9b00634b6a5144f75b1)
- [Fix] `concat`: add missing slot for `.return`; convert singular slot to plural slot [`cc4b586`](https://github.com/es-shims/iterator-helpers/commit/cc4b586cdb0ad6ba540043077c3d71ff79d528cd)
- [Dev Deps] update `@es-shims/api` [`dd14f1b`](https://github.com/es-shims/iterator-helpers/commit/dd14f1b9f06c37f3bcb0a20bacd6e7f031c0724d)

## [v1.1.0](https://github.com/es-shims/iterator-helpers/compare/v1.0.19...v1.1.0) - 2024-10-09

### Commits

- [New] add `Iterator.concat` [`1c07c21`](https://github.com/es-shims/iterator-helpers/commit/1c07c21d42fa6f3de516191a2b7fb848679314e7)
- [readme] add ESM and CJS examples [`ae0b60c`](https://github.com/es-shims/iterator-helpers/commit/ae0b60cae42a5e6174809859e29d16085042e3b2)
- [Dev Deps] update `@es-shims/api`, `auto-changelog`, `es-value-fixtures`, `eslint-plugin-import`, `tape` [`bd34766`](https://github.com/es-shims/iterator-helpers/commit/bd34766d527cc625dcbc242297290a793f21b055)
- [Dev Deps] update `@ljharb/eslint-config`, `mock-property`, `object-inspect`, `tape` [`2016080`](https://github.com/es-shims/iterator-helpers/commit/201608082a32bc1fa114c012eeb72744e1bf5bfa)
- [readme] fix copy-paste errors [`799255c`](https://github.com/es-shims/iterator-helpers/commit/799255ce358f9f19ab54cc2377aafb404463d917)
- [Tests] replace `aud` with `npm audit` [`4c48a77`](https://github.com/es-shims/iterator-helpers/commit/4c48a77d90a6894863ad160fd0f88b3fc76653b6)
- [Deps] update `globalthis` [`14d9e97`](https://github.com/es-shims/iterator-helpers/commit/14d9e971f976da482cbb7ee76b583db9ce17e056)
- [Dev Deps] update `@es-shims/api` [`15d84bb`](https://github.com/es-shims/iterator-helpers/commit/15d84bbbe1864dfd028f30f98251cbdf19e74908)
- [Dev Deps] add missing peer dep [`34559b9`](https://github.com/es-shims/iterator-helpers/commit/34559b9a263dfbc3551fd0ff586639692c04e514)

## [v1.0.19](https://github.com/es-shims/iterator-helpers/compare/v1.0.18...v1.0.19) - 2024-04-24

### Commits

- [patch] remove unused AOs [`698cef7`](https://github.com/es-shims/iterator-helpers/commit/698cef79757378a74500690d5a5dc2a6b86cd304)
- [Fix] `drop`, `filter`, `flatMap`, `map`: rpatch a v8 bug when polyfilling [`3670395`](https://github.com/es-shims/iterator-helpers/commit/36703956321c201933c4c701c78304669a46947b)
- [Deps] update `es-abstract` [`d2b47a5`](https://github.com/es-shims/iterator-helpers/commit/d2b47a5f46d3f874aad67375346cb58bf8c9e8b2)

## [v1.0.18](https://github.com/es-shims/iterator-helpers/compare/v1.0.17...v1.0.18) - 2024-03-15

### Commits

- [Deps] update `es-abstract` [`6b45f15`](https://github.com/es-shims/iterator-helpers/commit/6b45f150e939cfbf93b816431a407c0838250e85)
- [Deps] update `es-set-tostringtag`, `has-proto`, `safe-array-concat` [`6350106`](https://github.com/es-shims/iterator-helpers/commit/6350106c9e5c18a67754ced3fd90465b0032f1e3)
- [Dev Deps] update `tape` [`5509b40`](https://github.com/es-shims/iterator-helpers/commit/5509b408791402d2f7d7882f905741e5a5dd805b)
- [Deps] remove an unused dep [`78e34a5`](https://github.com/es-shims/iterator-helpers/commit/78e34a5452e7b7ef3dc239a62dd8a7324fbb2305)

## [v1.0.17](https://github.com/es-shims/iterator-helpers/compare/v1.0.16...v1.0.17) - 2024-02-13

### Fixed

- [Fix] avoid use of internal `assertRecord` helper from `es-abstract` [`#5`](https://github.com/es-shims/iterator-helpers/issues/5)

### Commits

- [Deps] update `call-bind`, `es-abstract`, `has-property-descriptors` [`e563ee7`](https://github.com/es-shims/iterator-helpers/commit/e563ee7230897c41f7f3623a11fc0ecc6862ee1c)

## [v1.0.16](https://github.com/es-shims/iterator-helpers/compare/v1.0.15...v1.0.16) - 2024-02-09

### Commits

- [Refactor] use `IteratorStepValue` [`aa62d72`](https://github.com/es-shims/iterator-helpers/commit/aa62d72c52d86218942b4792c7131f72d939b733)
- [Refactor] use `es-errors` instead of `get-intrinsic` where possible [`05a25ae`](https://github.com/es-shims/iterator-helpers/commit/05a25aeccd0523df86b401c771d88fbea06558c9)
- [Deps] update `call-bind`, `es-abstract`, `es-set-tostringtag`, `function-bind`, `get-intrinsic`, `has-property-descriptors`, `internal-slot`, `safe-array-concat` [`6aeee43`](https://github.com/es-shims/iterator-helpers/commit/6aeee435ed5956b7c5ee66a51b86c26827874eae)
- [Dev Deps] update `aud`, `eslint-plugin-import`, `mock-property`, `npmignore`, `object-inspect`, `tape` [`0a21fdb`](https://github.com/es-shims/iterator-helpers/commit/0a21fdb4a59d25c248561b7f476fc9670b411855)
- [Deps] update `call-bind`, `es-errors`, `get-intrinsic`, `internal-slot` [`5b10625`](https://github.com/es-shims/iterator-helpers/commit/5b106251dad4252a744b2e0aa0d1be4c1c62b779)
- [meta] add missing `engines.node` [`1534039`](https://github.com/es-shims/iterator-helpers/commit/1534039eca062429c0df68dcc3459ce9ba7c69be)
- [Deps] update `get-intrinsic` [`37da5f2`](https://github.com/es-shims/iterator-helpers/commit/37da5f201940c78526de647ef9ba84d9152349c1)
- [Dev Deps] update `has-tostringtag` [`a7cac51`](https://github.com/es-shims/iterator-helpers/commit/a7cac51cc027e6274918cb1ea0cf1fa1a194fcd9)

## [v1.0.15](https://github.com/es-shims/iterator-helpers/compare/v1.0.14...v1.0.15) - 2023-09-13

### Commits

- [New] add `Iterator.prototype` shim [`c4a6203`](https://github.com/es-shims/iterator-helpers/commit/c4a6203a0ac87bf0a33835e775c207ff1911225d)
- [Tests] add passing tests for native generators [`57bae8c`](https://github.com/es-shims/iterator-helpers/commit/57bae8ccbeb27ed0e6c449f35cddbda6cf6757e8)
- [Deps] update `define-properties`, `iterator.prototype`, `safe-array-concat` [`56ca087`](https://github.com/es-shims/iterator-helpers/commit/56ca087f924dbefee47f0a1cb3b8468de76cd234)

## [v1.0.14](https://github.com/es-shims/iterator-helpers/compare/v1.0.13...v1.0.14) - 2023-08-26

### Commits

- [Deps] update `es-abstract` [`477b123`](https://github.com/es-shims/iterator-helpers/commit/477b1233acd36fdbbccd79fbb69cde325bc3e6a9)
- [Dev Deps] update `aud`, `eslint-plugin-import`, `tape` [`e4ea414`](https://github.com/es-shims/iterator-helpers/commit/e4ea4146feffb72fd828e5d883e960e0ad589a35)

## [v1.0.13](https://github.com/es-shims/iterator-helpers/compare/v1.0.12...v1.0.13) - 2023-08-16

### Fixed

- [Deps] add missing deps; add eslint-plugin-import [`#3`](https://github.com/es-shims/iterator-helpers/issues/3)

## [v1.0.12](https://github.com/es-shims/iterator-helpers/compare/v1.0.11...v1.0.12) - 2023-07-14

### Commits

- [Fix] avoid creating string wrapper objects with sloppy mode flatMap mappers [`db16b34`](https://github.com/es-shims/iterator-helpers/commit/db16b34aec554934ec2bfd62629fb66cebc311f8)
- [Deps] update `es-abstract` [`f002147`](https://github.com/es-shims/iterator-helpers/commit/f002147f0afbb2cd7c2d2e1207685f52e33abf0f)
- [Dev Deps] update `@ljharb/eslint-config`, `aud [`42064e8`](https://github.com/es-shims/iterator-helpers/commit/42064e80e0cc37f5e4676c2133dacae7456e313f)
- [meta] fix tidelift funding identifier [`896fd4f`](https://github.com/es-shims/iterator-helpers/commit/896fd4f2e4b419945bfbd85024a2c96248323151)

## [v1.0.11](https://github.com/es-shims/iterator-helpers/compare/v1.0.10...v1.0.11) - 2023-05-22

### Commits

- [Fix] iterator helpers are not a constructor [`8a7f999`](https://github.com/es-shims/iterator-helpers/commit/8a7f9996ba3600ef30f3a9c75f9f994e88d075c6)

## [v1.0.10](https://github.com/es-shims/iterator-helpers/compare/v1.0.9...v1.0.10) - 2023-05-18

### Commits

- [patch] remove IsCallable check on NextMethod, deferring errors to callsite [`bbb7efa`](https://github.com/es-shims/iterator-helpers/commit/bbb7efac8349273fe17c86194ef13af45bcb8e24)
- [patch] change Symbol.iterator fallback from callable check to nullish check [`ec3e255`](https://github.com/es-shims/iterator-helpers/commit/ec3e255dfe30ea6650d8a697e6c4f16fa393e923)
- [Tests] add test cases [`5117c47`](https://github.com/es-shims/iterator-helpers/commit/5117c477348407ebdfc9410dd437a68634c39a8e)
- [Dev Deps] update `@es-shims/api` [`9fa13a0`](https://github.com/es-shims/iterator-helpers/commit/9fa13a0739f353536de58b2b648aa9eacfa49479)
- [Dev Deps] update `@es-shims/api` [`b74b0ac`](https://github.com/es-shims/iterator-helpers/commit/b74b0ac2bd7e920f760bae7ba7c6c5310f5123d8)

## [v1.0.9](https://github.com/es-shims/iterator-helpers/compare/v1.0.8...v1.0.9) - 2023-05-02

### Commits

- [Refactor] use 2022 AO instead of 2015 AO [`75ee5c4`](https://github.com/es-shims/iterator-helpers/commit/75ee5c4dea0037f02a61c240114bb6bd8c8b48f1)

## [v1.0.8](https://github.com/es-shims/iterator-helpers/compare/v1.0.7...v1.0.8) - 2023-05-02

### Commits

- [Fix] `flatMap`: close the inner iterator when applicable [`4dc94e0`](https://github.com/es-shims/iterator-helpers/commit/4dc94e0117e34b4c99f9ed96c4fe306896c47da1)

## [v1.0.7](https://github.com/es-shims/iterator-helpers/compare/v1.0.6...v1.0.7) - 2023-05-01

### Commits

- [Fix] `flatMap`: properly handle yielded iterables [`3a78767`](https://github.com/es-shims/iterator-helpers/commit/3a78767e86394d45b212a225e4253745f8b1dc8d)
- [Fix] `flatMap`: only increment the count when iterating the outer iterator [`955d0b0`](https://github.com/es-shims/iterator-helpers/commit/955d0b00f5660db0d9febef2a16426dfc32e8be4)

## [v1.0.6](https://github.com/es-shims/iterator-helpers/compare/v1.0.5...v1.0.6) - 2023-04-20

### Commits

- [Refactor] `GetIteratorFlattenable`: remove hint [`781fc7c`](https://github.com/es-shims/iterator-helpers/commit/781fc7c28615aaaef139cbcd9e6ade513419bea1)
- [Refactor] `GetIteratorFlattenable`: use `GetIteratorDirect` [`026118b`](https://github.com/es-shims/iterator-helpers/commit/026118b8c884adaee5ab0eb12de838ef9abdbb6f)
- [Refactor] use `safe-array-concat` [`1d985a4`](https://github.com/es-shims/iterator-helpers/commit/1d985a449be212523f05ba4a359a162931ddf3d3)

## [v1.0.5](https://github.com/es-shims/iterator-helpers/compare/v1.0.4...v1.0.5) - 2023-03-22

### Commits

- [Tests] add passing tests for 4240029 [`c2082fe`](https://github.com/es-shims/iterator-helpers/commit/c2082fee3e73dc1998a67fbe0014e3ebdceb8ec0)
- [Fix] properly allow subclasses of Iterator to be constructed [`5cebe2a`](https://github.com/es-shims/iterator-helpers/commit/5cebe2a5767393696d0ce4e9325edf78c300f938)

## [v1.0.4](https://github.com/es-shims/iterator-helpers/compare/v1.0.3...v1.0.4) - 2023-03-21

### Commits

- [Fix] validate arguments first [`4240029`](https://github.com/es-shims/iterator-helpers/commit/42400297454909ddccc899a012dc55bbd403eb8b)
- [Fix] close underlying iterator when helper is closed [`f5372c7`](https://github.com/es-shims/iterator-helpers/commit/f5372c78cafff64bfda5849386538f806916049a)
- [Tests] `Iterator`: remove an unnecessary call-bind [`7d0ba59`](https://github.com/es-shims/iterator-helpers/commit/7d0ba59f672e690b189f91e0348f6b5e00f934e1)

## [v1.0.3](https://github.com/es-shims/iterator-helpers/compare/v1.0.2...v1.0.3) - 2023-03-17

### Commits

- [Fix] `drop`/`filter`/`flatMap`/`map`/`take`: properly IfAbruptCloseIterator [`ff643a0`](https://github.com/es-shims/iterator-helpers/commit/ff643a0b7c4e5c2b00e794ba9b988b47e783f235)
- [Refactor] use `NormalCompletion`/`ThrowCompletion` instead of thunks [`68fd937`](https://github.com/es-shims/iterator-helpers/commit/68fd937b53c107481a9f868bda5b2bd5cbc00142)
- [Fix] `filter`: IteratorClose needs to rethrow the error [`200474f`](https://github.com/es-shims/iterator-helpers/commit/200474ff289dea77c696c0c025f4602405cf3fff)
- [Fix] `filter`: properly increment the counter [`14aa2d8`](https://github.com/es-shims/iterator-helpers/commit/14aa2d8b75fd16378c2be183fd5b008712547ed4)
- [Fix] `Iterator` can not be `new`ed or invoked directly [`6fbd68e`](https://github.com/es-shims/iterator-helpers/commit/6fbd68e778dd455c4aa63f4e1f39e0b583610509)
- [Fix] `Iterator.prototype` should be non-writable [`1080288`](https://github.com/es-shims/iterator-helpers/commit/108028858067e40ea56dca9a68dd6cea4966e904)
- [Deps] update `es-abstract` [`b7913da`](https://github.com/es-shims/iterator-helpers/commit/b7913da9cddef1ec40b4827821c6069019b79093)
- [Dev Deps] update `@es-shims/api` [`0071bed`](https://github.com/es-shims/iterator-helpers/commit/0071bed9e13231317d2cdb9ae0ecb6603784ad1f)

## [v1.0.2](https://github.com/es-shims/iterator-helpers/compare/v1.0.1...v1.0.2) - 2023-02-09

### Commits

- [Refactor] inline 2023 impls of Iterator AOs until es-abstract is published with them [`b9c80c5`](https://github.com/es-shims/iterator-helpers/commit/b9c80c5aba0deaaabef7e650fe7ec231fdc695e3)
- [Fix] ensure calling `.return` does not invoke the next iteration [`9e28ed5`](https://github.com/es-shims/iterator-helpers/commit/9e28ed5af44a660a0d2e80684cb9a4bf3d86e09a)
- [Fix] `map`: pass the proper index argument to the mapper [`125e3ca`](https://github.com/es-shims/iterator-helpers/commit/125e3cac192ef650a88f774a5a2dd9afe395a5b8)
- [Deps] update `internal-slot` [`43351b6`](https://github.com/es-shims/iterator-helpers/commit/43351b63545e3698f54daf5dc0652a7b2fb7cb28)

## [v1.0.1](https://github.com/es-shims/iterator-helpers/compare/v1.0.0...v1.0.1) - 2023-02-07

### Commits

- [Fix] `Iterator`: throw when Iterator() is called without new [`a6fc7e7`](https://github.com/es-shims/iterator-helpers/commit/a6fc7e768cbf4d43117365ec2f1bd300247d8dfd)

## v1.0.0 - 2023-02-05

### Commits

- Initial implementation, tests, readme [`650713e`](https://github.com/es-shims/iterator-helpers/commit/650713eecc9d4dab28d5ba3dc5afcbdb8ff99b5a)
- Initial commit [`2379dfd`](https://github.com/es-shims/iterator-helpers/commit/2379dfdad70f64efb31e342a4a7779b1140b2481)
- npm init [`f77411a`](https://github.com/es-shims/iterator-helpers/commit/f77411a443f1a103dbb92a69210228d4fc1e6d04)
- Only apps should have lockfiles [`313dcf5`](https://github.com/es-shims/iterator-helpers/commit/313dcf5211e99569ad275885728b5ac7af30f4ec)
