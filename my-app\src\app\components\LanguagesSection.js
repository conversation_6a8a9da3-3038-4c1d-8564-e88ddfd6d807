'use client';

import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export default function LanguagesSection() {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const languagesRef = useRef(null);
  const globeRef = useRef(null);

  useEffect(() => {
    const languages = languagesRef.current.children;
    
    gsap.fromTo(titleRef.current,
      { y: 50, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      }
    );

    gsap.fromTo(globeRef.current,
      { scale: 0.8, opacity: 0, rotation: -180 },
      {
        scale: 1,
        opacity: 1,
        rotation: 0,
        duration: 1.5,
        ease: "power2.out",
        scrollTrigger: {
          trigger: globeRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      }
    );

    gsap.fromTo(languages,
      { y: 30, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.6,
        stagger: 0.1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: languagesRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      }
    );

    // Continuous rotation for globe
    gsap.to(globeRef.current, {
      rotation: 360,
      duration: 20,
      ease: "none",
      repeat: -1
    });
  }, []);

  const languages = [
    { name: "English", flag: "🇺🇸", accuracy: "99.8%" },
    { name: "Spanish", flag: "🇪🇸", accuracy: "99.5%" },
    { name: "French", flag: "🇫🇷", accuracy: "99.3%" },
    { name: "German", flag: "🇩🇪", accuracy: "99.4%" },
    { name: "Italian", flag: "🇮🇹", accuracy: "99.2%" },
    { name: "Portuguese", flag: "🇵🇹", accuracy: "99.1%" },
    { name: "Russian", flag: "🇷🇺", accuracy: "98.9%" },
    { name: "Chinese", flag: "🇨🇳", accuracy: "99.0%" },
    { name: "Japanese", flag: "🇯🇵", accuracy: "98.8%" },
    { name: "Korean", flag: "🇰🇷", accuracy: "98.7%" },
    { name: "Arabic", flag: "🇸🇦", accuracy: "98.5%" },
    { name: "Hindi", flag: "🇮🇳", accuracy: "98.6%" },
    { name: "Dutch", flag: "🇳🇱", accuracy: "99.1%" },
    { name: "Swedish", flag: "🇸🇪", accuracy: "99.0%" },
    { name: "Norwegian", flag: "🇳🇴", accuracy: "98.9%" },
    { name: "Danish", flag: "🇩🇰", accuracy: "98.8%" },
    { name: "Finnish", flag: "🇫🇮", accuracy: "98.7%" },
    { name: "Polish", flag: "🇵🇱", accuracy: "98.9%" },
    { name: "Turkish", flag: "🇹🇷", accuracy: "98.6%" },
    { name: "Greek", flag: "🇬🇷", accuracy: "98.5%" },
    { name: "Hebrew", flag: "🇮🇱", accuracy: "98.4%" },
    { name: "Thai", flag: "🇹🇭", accuracy: "98.3%" },
    { name: "Vietnamese", flag: "🇻🇳", accuracy: "98.2%" },
    { name: "Indonesian", flag: "🇮🇩", accuracy: "98.4%" }
  ];

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-slate-800 dark:via-slate-900 dark:to-purple-900">
      <div className="max-w-7xl mx-auto px-6">
        <div ref={titleRef} className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="gradient-text">Global Language Support</span>
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
            Break down language barriers with our comprehensive support for over 100 languages
            and dialects, powered by advanced AI models trained on diverse datasets.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Globe Visualization */}
          <div className="flex justify-center">
            <div ref={globeRef} className="relative w-80 h-80">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-20 blur-xl"></div>
              <div className="absolute inset-4 bg-gradient-to-br from-blue-500 to-purple-700 rounded-full flex items-center justify-center">
                <svg className="w-48 h-48 text-white opacity-80" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM11 19.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                </svg>
              </div>
              <div className="absolute inset-0 border-4 border-blue-300 dark:border-blue-600 rounded-full animate-pulse"></div>
            </div>
          </div>

          {/* Languages Grid */}
          <div ref={languagesRef} className="grid grid-cols-2 sm:grid-cols-3 gap-4">
            {languages.map((language, index) => (
              <div
                key={index}
                className="group bg-white dark:bg-slate-800 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer"
              >
                <div className="text-center">
                  <div className="text-3xl mb-2 group-hover:scale-110 transition-transform duration-300">
                    {language.flag}
                  </div>
                  <h3 className="font-semibold text-slate-800 dark:text-white mb-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                    {language.name}
                  </h3>
                  <p className="text-sm text-green-600 dark:text-green-400 font-medium">
                    {language.accuracy}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
          <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg">
            <div className="text-3xl font-bold gradient-text mb-2">100+</div>
            <div className="text-slate-600 dark:text-slate-400">Languages</div>
          </div>
          <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg">
            <div className="text-3xl font-bold gradient-text mb-2">50+</div>
            <div className="text-slate-600 dark:text-slate-400">Dialects</div>
          </div>
          <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg">
            <div className="text-3xl font-bold gradient-text mb-2">Auto</div>
            <div className="text-slate-600 dark:text-slate-400">Detection</div>
          </div>
          <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg">
            <div className="text-3xl font-bold gradient-text mb-2">Real-time</div>
            <div className="text-slate-600 dark:text-slate-400">Processing</div>
          </div>
        </div>
      </div>
    </section>
  );
}
