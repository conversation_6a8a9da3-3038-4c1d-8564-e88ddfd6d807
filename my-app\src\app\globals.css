@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
  /* Primary Brand Colors */
  --primary-blue: #2563eb;
  --primary-blue-dark: #1d4ed8;
  --primary-blue-light: #3b82f6;

  /* Secondary Colors */
  --secondary-purple: #7c3aed;
  --secondary-green: #10b981;
  --secondary-orange: #f59e0b;

  /* Neutral Colors */
  --background: #ffffff;
  --background-secondary: #f8fafc;
  --background-dark: #0f172a;
  --foreground: #1e293b;
  --foreground-light: #64748b;
  --foreground-dark: #f1f5f9;

  /* Accent Colors */
  --accent-gradient-start: #2563eb;
  --accent-gradient-end: #7c3aed;

  /* Border Colors */
  --border-light: #e2e8f0;
  --border-dark: #334155;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--background-dark);
    --background-secondary: #1e293b;
    --foreground: var(--foreground-dark);
    --foreground-light: #94a3b8;
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-blue-dark);
}

/* Gradient text utility */
.gradient-text {
  background: linear-gradient(135deg, var(--accent-gradient-start), var(--accent-gradient-end));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button animations */
.btn-hover-effect {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-hover-effect:hover::before {
  left: 100%;
}

/* Section spacing utilities */
.section-padding {
  padding: 5rem 0;
}

@media (max-width: 768px) {
  .section-padding {
    padding: 3rem 0;
  }
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Loading animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Focus styles for accessibility */
.focus-ring:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Text selection */
::selection {
  background: var(--primary-blue);
  color: white;
}

::-moz-selection {
  background: var(--primary-blue);
  color: white;
}

/* Responsive text sizes */
.text-responsive-xl {
  font-size: clamp(2rem, 5vw, 4rem);
}

.text-responsive-lg {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
}

.text-responsive-md {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
}

/* Container utilities */
.container-custom {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Animation delays for staggered effects */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }

/* Utility for hiding elements visually but keeping them accessible */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
