@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
  /* Primary Brand Colors */
  --primary-blue: #2563eb;
  --primary-blue-dark: #1d4ed8;
  --primary-blue-light: #3b82f6;

  /* Secondary Colors */
  --secondary-purple: #7c3aed;
  --secondary-green: #10b981;
  --secondary-orange: #f59e0b;

  /* Neutral Colors */
  --background: #ffffff;
  --background-secondary: #f8fafc;
  --background-dark: #0f172a;
  --foreground: #1e293b;
  --foreground-light: #64748b;
  --foreground-dark: #f1f5f9;

  /* Accent Colors */
  --accent-gradient-start: #2563eb;
  --accent-gradient-end: #7c3aed;

  /* Border Colors */
  --border-light: #e2e8f0;
  --border-dark: #334155;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--background-dark);
    --background-secondary: #1e293b;
    --foreground: var(--foreground-dark);
    --foreground-light: #94a3b8;
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-blue-dark);
}

/* Gradient text utility */
.gradient-text {
  background: linear-gradient(135deg, var(--accent-gradient-start), var(--accent-gradient-end));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button animations */
.btn-hover-effect {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-hover-effect:hover::before {
  left: 100%;
}
