'use client';

import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export default function FAQSection() {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const faqsRef = useRef(null);
  const [openFAQ, setOpenFAQ] = useState(0);

  useEffect(() => {
    gsap.fromTo(titleRef.current,
      { y: 50, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      }
    );

    gsap.fromTo(faqsRef.current.children,
      { y: 30, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.6,
        stagger: 0.1,
        scrollTrigger: {
          trigger: faqsRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      }
    );
  }, []);

  const faqs = [
    {
      question: "How accurate is the AI transcription?",
      answer: "Our AI achieves 99.5% accuracy on clear audio recordings. Accuracy may vary based on audio quality, background noise, accents, and technical terminology. We continuously improve our models with regular updates and training on diverse datasets."
    },
    {
      question: "What audio formats do you support?",
      answer: "We support all major audio formats including MP3, WAV, FLAC, M4A, AAC, OGG, WMA, and more. You can also upload video files (MP4, AVI, MOV, MKV) and we'll extract the audio automatically for transcription."
    },
    {
      question: "How long does transcription take?",
      answer: "Our AI processes audio at 10x real-time speed. A 1-hour audio file typically takes 6-10 minutes to transcribe, depending on audio quality and current server load. You'll receive real-time progress updates during processing."
    },
    {
      question: "Is my data secure and private?",
      answer: "Absolutely. We use end-to-end encryption for all uploads and processing. Your files are automatically deleted from our servers after 24 hours. We offer local processing options for sensitive content and are fully GDPR and CCPA compliant."
    },
    {
      question: "Can you identify different speakers?",
      answer: "Yes! Our speaker diarization feature can identify and separate different speakers in your audio. This is perfect for meetings, interviews, and multi-person conversations. Each speaker is labeled clearly in the transcript."
    },
    {
      question: "What languages do you support?",
      answer: "We support over 100 languages and dialects including English, Spanish, French, German, Italian, Portuguese, Russian, Chinese, Japanese, Korean, Arabic, Hindi, and many more. Our AI automatically detects the language or you can specify it manually."
    },
    {
      question: "Do you offer a free trial?",
      answer: "Yes! We offer a free trial that includes 30 minutes of transcription time. No credit card required to start. You can test our accuracy and features before committing to a paid plan."
    },
    {
      question: "Can I edit the transcription?",
      answer: "Absolutely! Our built-in editor allows you to make corrections, add timestamps, format text, and export in multiple formats (TXT, DOCX, SRT, VTT). You can also collaborate with team members on transcript editing."
    },
    {
      question: "What's the maximum file size?",
      answer: "Free accounts can upload files up to 100MB. Paid plans support files up to 2GB. For larger files, you can split them or contact our support team for enterprise solutions that handle files of any size."
    },
    {
      question: "Do you offer API access?",
      answer: "Yes! We provide a robust REST API for developers who want to integrate transcription into their applications. Our API supports real-time streaming, batch processing, and webhook notifications for completed transcriptions."
    }
  ];

  const toggleFAQ = (index) => {
    setOpenFAQ(openFAQ === index ? -1 : index);
  };

  return (
    <section ref={sectionRef} className="py-20 bg-white dark:bg-slate-900">
      <div className="max-w-4xl mx-auto px-6">
        <div ref={titleRef} className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="gradient-text">Frequently Asked Questions</span>
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
            Got questions? We've got answers. Find everything you need to know about our
            AI transcription service below.
          </p>
        </div>

        <div ref={faqsRef} className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="bg-gradient-to-br from-slate-50 to-white dark:from-slate-800 dark:to-slate-900 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 overflow-hidden"
            >
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors duration-300 group"
              >
                <h3 className="text-lg font-semibold text-slate-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300 pr-4">
                  {faq.question}
                </h3>
                <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white transition-transform duration-300 ${openFAQ === index ? 'rotate-180' : ''}`}>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>
              
              <div className={`overflow-hidden transition-all duration-500 ease-in-out ${openFAQ === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                <div className="px-8 pb-6">
                  <div className="w-full h-px bg-gradient-to-r from-blue-200 to-purple-200 dark:from-blue-800 dark:to-purple-800 mb-4"></div>
                  <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Contact Support */}
        <div className="mt-16 text-center bg-gradient-to-r from-blue-50 to-purple-50 dark:from-slate-800 dark:to-slate-900 rounded-2xl p-8 border border-slate-200 dark:border-slate-700">
          <h3 className="text-2xl font-bold mb-4 text-slate-800 dark:text-white">
            Still have questions?
          </h3>
          <p className="text-slate-600 dark:text-slate-400 mb-6">
            Our support team is here to help you 24/7. Get in touch and we'll respond within minutes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-hover-effect bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center justify-center gap-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Live Chat Support
            </button>
            <button className="btn-hover-effect bg-white dark:bg-slate-800 border-2 border-slate-300 dark:border-slate-600 hover:border-blue-300 dark:hover:border-blue-600 text-slate-700 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center justify-center gap-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Email Support
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
