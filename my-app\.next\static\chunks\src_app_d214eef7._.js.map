{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/components/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\n\nexport default function HeroSection() {\n  const heroRef = useRef(null);\n  const titleRef = useRef(null);\n  const subtitleRef = useRef(null);\n  const buttonsRef = useRef(null);\n  const backgroundRef = useRef(null);\n\n  useEffect(() => {\n    const tl = gsap.timeline();\n    \n    // Background animation\n    gsap.set(backgroundRef.current, { scale: 1.1, opacity: 0 });\n    tl.to(backgroundRef.current, { scale: 1, opacity: 1, duration: 1.5, ease: \"power2.out\" });\n    \n    // Text animations\n    tl.fromTo(titleRef.current, \n      { y: 100, opacity: 0 },\n      { y: 0, opacity: 1, duration: 1, ease: \"power3.out\" },\n      \"-=1\"\n    );\n    \n    tl.fromTo(subtitleRef.current,\n      { y: 50, opacity: 0 },\n      { y: 0, opacity: 1, duration: 0.8, ease: \"power2.out\" },\n      \"-=0.5\"\n    );\n    \n    tl.fromTo(buttonsRef.current.children,\n      { y: 30, opacity: 0 },\n      { y: 0, opacity: 1, duration: 0.6, stagger: 0.2, ease: \"power2.out\" },\n      \"-=0.3\"\n    );\n\n    // Floating animation for background elements\n    gsap.to('.floating-element', {\n      y: -20,\n      duration: 3,\n      ease: \"power1.inOut\",\n      yoyo: true,\n      repeat: -1,\n      stagger: 0.5\n    });\n  }, []);\n\n  return (\n    <section ref={heroRef} className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-purple-900\">\n      {/* Background Elements */}\n      <div ref={backgroundRef} className=\"absolute inset-0\">\n        <div className=\"floating-element absolute top-20 left-10 w-32 h-32 bg-blue-200 dark:bg-blue-800 rounded-full opacity-20 blur-xl\"></div>\n        <div className=\"floating-element absolute top-40 right-20 w-24 h-24 bg-purple-200 dark:bg-purple-800 rounded-full opacity-30 blur-lg\"></div>\n        <div className=\"floating-element absolute bottom-32 left-1/4 w-40 h-40 bg-green-200 dark:bg-green-800 rounded-full opacity-15 blur-2xl\"></div>\n        <div className=\"floating-element absolute bottom-20 right-1/3 w-28 h-28 bg-orange-200 dark:bg-orange-800 rounded-full opacity-25 blur-xl\"></div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 max-w-6xl mx-auto px-6 text-center\">\n        <div className=\"mb-8\">\n          <h1 \n            ref={titleRef}\n            className=\"text-5xl md:text-7xl font-bold mb-6 leading-tight\"\n          >\n            <span className=\"gradient-text\">AI-Powered</span>\n            <br />\n            <span className=\"text-slate-800 dark:text-white\">Audio to Text</span>\n          </h1>\n          \n          <p \n            ref={subtitleRef}\n            className=\"text-xl md:text-2xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Transform your audio files into accurate text with our advanced AI technology. \n            Supporting <span className=\"font-semibold text-blue-600 dark:text-blue-400\">100+ languages</span> \n            with lightning-fast processing and industry-leading accuracy.\n          </p>\n        </div>\n\n        {/* Download Buttons */}\n        <div ref={buttonsRef} className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n          <button className=\"btn-hover-effect group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center gap-3 min-w-[200px]\">\n            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n            Download for Windows\n          </button>\n          \n          <button className=\"btn-hover-effect group bg-gradient-to-r from-slate-700 to-slate-800 hover:from-slate-800 hover:to-slate-900 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center gap-3 min-w-[200px]\">\n            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n            Download for Mac\n          </button>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">100+</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Languages Supported</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">99.5%</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Accuracy Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">10x</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Faster Processing</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <svg className=\"w-6 h-6 text-slate-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n        </svg>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ;YAExB,uBAAuB;YACvB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,cAAc,OAAO,EAAE;gBAAE,OAAO;gBAAK,SAAS;YAAE;YACzD,GAAG,EAAE,CAAC,cAAc,OAAO,EAAE;gBAAE,OAAO;gBAAG,SAAS;gBAAG,UAAU;gBAAK,MAAM;YAAa;YAEvF,kBAAkB;YAClB,GAAG,MAAM,CAAC,SAAS,OAAO,EACxB;gBAAE,GAAG;gBAAK,SAAS;YAAE,GACrB;gBAAE,GAAG;gBAAG,SAAS;gBAAG,UAAU;gBAAG,MAAM;YAAa,GACpD;YAGF,GAAG,MAAM,CAAC,YAAY,OAAO,EAC3B;gBAAE,GAAG;gBAAI,SAAS;YAAE,GACpB;gBAAE,GAAG;gBAAG,SAAS;gBAAG,UAAU;gBAAK,MAAM;YAAa,GACtD;YAGF,GAAG,MAAM,CAAC,WAAW,OAAO,CAAC,QAAQ,EACnC;gBAAE,GAAG;gBAAI,SAAS;YAAE,GACpB;gBAAE,GAAG;gBAAG,SAAS;gBAAG,UAAU;gBAAK,SAAS;gBAAK,MAAM;YAAa,GACpE;YAGF,6CAA6C;YAC7C,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,qBAAqB;gBAC3B,GAAG,CAAC;gBACJ,UAAU;gBACV,MAAM;gBACN,MAAM;gBACN,QAAQ,CAAC;gBACT,SAAS;YACX;QACF;gCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;QAAS,WAAU;;0BAE/B,6LAAC;gBAAI,KAAK;gBAAe,WAAU;;kCACjC,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,KAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;;;;;kDACD,6LAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;;0CAGnD,6LAAC;gCACC,KAAK;gCACL,WAAU;;oCACX;kDAEY,6LAAC;wCAAK,WAAU;kDAAiD;;;;;;oCAAqB;;;;;;;;;;;;;kCAMrG,6LAAC;wBAAI,KAAK;wBAAY,WAAU;;0CAC9B,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAwL,UAAS;;;;;;;;;;;oCACxN;;;;;;;0CAIR,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAwL,UAAS;;;;;;;;;;;oCACxN;;;;;;;;;;;;;kCAMV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;0BAM1D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAyB,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAChF,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;AAK/E;GAtHwB;KAAA", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/components/FeaturesSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function FeaturesSection() {\n  const sectionRef = useRef(null);\n  const titleRef = useRef(null);\n  const cardsRef = useRef(null);\n\n  useEffect(() => {\n    const cards = cardsRef.current.children;\n    \n    gsap.fromTo(titleRef.current,\n      { y: 50, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 1,\n        scrollTrigger: {\n          trigger: titleRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    gsap.fromTo(cards,\n      { y: 80, opacity: 0, scale: 0.9 },\n      {\n        y: 0,\n        opacity: 1,\n        scale: 1,\n        duration: 0.8,\n        stagger: 0.2,\n        ease: \"power2.out\",\n        scrollTrigger: {\n          trigger: cardsRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    // Hover animations for cards\n    Array.from(cards).forEach(card => {\n      card.addEventListener('mouseenter', () => {\n        gsap.to(card, { y: -10, scale: 1.05, duration: 0.3, ease: \"power2.out\" });\n      });\n      \n      card.addEventListener('mouseleave', () => {\n        gsap.to(card, { y: 0, scale: 1, duration: 0.3, ease: \"power2.out\" });\n      });\n    });\n  }, []);\n\n  const features = [\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n        </svg>\n      ),\n      title: \"Lightning Fast\",\n      description: \"Process hours of audio in minutes with our optimized AI algorithms and cloud infrastructure.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n        </svg>\n      ),\n      title: \"99.5% Accuracy\",\n      description: \"Industry-leading accuracy with advanced noise reduction and context-aware processing.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129\" />\n        </svg>\n      ),\n      title: \"100+ Languages\",\n      description: \"Support for over 100 languages and dialects with automatic language detection.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n        </svg>\n      ),\n      title: \"Secure & Private\",\n      description: \"End-to-end encryption with local processing options. Your data never leaves your device.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n        </svg>\n      ),\n      title: \"Easy to Use\",\n      description: \"Intuitive interface with drag-and-drop functionality. No technical expertise required.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n        </svg>\n      ),\n      title: \"Multiple Formats\",\n      description: \"Support for all major audio formats including MP3, WAV, FLAC, M4A, and more.\"\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-white dark:bg-slate-900\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div ref={titleRef} className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Powerful Features</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto\">\n            Experience the next generation of audio transcription with cutting-edge AI technology\n            designed for accuracy, speed, and ease of use.\n          </p>\n        </div>\n\n        <div ref={cardsRef} className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <div\n              key={index}\n              className=\"group bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-200 dark:border-slate-700 cursor-pointer\"\n            >\n              <div className=\"text-blue-600 dark:text-blue-400 mb-6 group-hover:scale-110 transition-transform duration-300\">\n                {feature.icon}\n              </div>\n              <h3 className=\"text-2xl font-bold mb-4 text-slate-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300\">\n                {feature.title}\n              </h3>\n              <p className=\"text-slate-600 dark:text-slate-400 leading-relaxed\">\n                {feature.description}\n              </p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AAElB,SAAS;;IACtB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,QAAQ,SAAS,OAAO,CAAC,QAAQ;YAEvC,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SAAS,OAAO,EAC1B;gBAAE,GAAG;gBAAI,SAAS;YAAE,GACpB;gBACE,GAAG;gBACH,SAAS;gBACT,UAAU;gBACV,eAAe;oBACb,SAAS,SAAS,OAAO;oBACzB,OAAO;oBACP,KAAK;oBACL,eAAe;gBACjB;YACF;YAGF,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OACV;gBAAE,GAAG;gBAAI,SAAS;gBAAG,OAAO;YAAI,GAChC;gBACE,GAAG;gBACH,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,MAAM;gBACN,eAAe;oBACb,SAAS,SAAS,OAAO;oBACzB,OAAO;oBACP,KAAK;oBACL,eAAe;gBACjB;YACF;YAGF,6BAA6B;YAC7B,MAAM,IAAI,CAAC,OAAO,OAAO;6CAAC,CAAA;oBACxB,KAAK,gBAAgB,CAAC;qDAAc;4BAClC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;gCAAE,GAAG,CAAC;gCAAI,OAAO;gCAAM,UAAU;gCAAK,MAAM;4BAAa;wBACzE;;oBAEA,KAAK,gBAAgB,CAAC;qDAAc;4BAClC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;gCAAE,GAAG;gCAAG,OAAO;gCAAG,UAAU;gCAAK,MAAM;4BAAa;wBACpE;;gBACF;;QACF;oCAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,KAAK;oBAAU,WAAU;;sCAC5B,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,6LAAC;4BAAE,WAAU;sCAA+D;;;;;;;;;;;;8BAM9E,6LAAC;oBAAI,KAAK;oBAAU,WAAU;8BAC3B,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;8CAEf,6LAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,6LAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAVjB;;;;;;;;;;;;;;;;;;;;;AAkBnB;GAhJwB;KAAA", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/components/LanguagesSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function LanguagesSection() {\n  const sectionRef = useRef(null);\n  const titleRef = useRef(null);\n  const languagesRef = useRef(null);\n  const globeRef = useRef(null);\n\n  useEffect(() => {\n    const languages = languagesRef.current.children;\n    \n    gsap.fromTo(titleRef.current,\n      { y: 50, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 1,\n        scrollTrigger: {\n          trigger: titleRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    gsap.fromTo(globeRef.current,\n      { scale: 0.8, opacity: 0, rotation: -180 },\n      {\n        scale: 1,\n        opacity: 1,\n        rotation: 0,\n        duration: 1.5,\n        ease: \"power2.out\",\n        scrollTrigger: {\n          trigger: globeRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    gsap.fromTo(languages,\n      { y: 30, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 0.6,\n        stagger: 0.1,\n        ease: \"power2.out\",\n        scrollTrigger: {\n          trigger: languagesRef.current,\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n\n    // Continuous rotation for globe\n    gsap.to(globeRef.current, {\n      rotation: 360,\n      duration: 20,\n      ease: \"none\",\n      repeat: -1\n    });\n  }, []);\n\n  const languages = [\n    { name: \"English\", flag: \"🇺🇸\", accuracy: \"99.8%\" },\n    { name: \"Spanish\", flag: \"🇪🇸\", accuracy: \"99.5%\" },\n    { name: \"French\", flag: \"🇫🇷\", accuracy: \"99.3%\" },\n    { name: \"German\", flag: \"🇩🇪\", accuracy: \"99.4%\" },\n    { name: \"Italian\", flag: \"🇮🇹\", accuracy: \"99.2%\" },\n    { name: \"Portuguese\", flag: \"🇵🇹\", accuracy: \"99.1%\" },\n    { name: \"Russian\", flag: \"🇷🇺\", accuracy: \"98.9%\" },\n    { name: \"Chinese\", flag: \"🇨🇳\", accuracy: \"99.0%\" },\n    { name: \"Japanese\", flag: \"🇯🇵\", accuracy: \"98.8%\" },\n    { name: \"Korean\", flag: \"🇰🇷\", accuracy: \"98.7%\" },\n    { name: \"Arabic\", flag: \"🇸🇦\", accuracy: \"98.5%\" },\n    { name: \"Hindi\", flag: \"🇮🇳\", accuracy: \"98.6%\" },\n    { name: \"Dutch\", flag: \"🇳🇱\", accuracy: \"99.1%\" },\n    { name: \"Swedish\", flag: \"🇸🇪\", accuracy: \"99.0%\" },\n    { name: \"Norwegian\", flag: \"🇳🇴\", accuracy: \"98.9%\" },\n    { name: \"Danish\", flag: \"🇩🇰\", accuracy: \"98.8%\" },\n    { name: \"Finnish\", flag: \"🇫🇮\", accuracy: \"98.7%\" },\n    { name: \"Polish\", flag: \"🇵🇱\", accuracy: \"98.9%\" },\n    { name: \"Turkish\", flag: \"🇹🇷\", accuracy: \"98.6%\" },\n    { name: \"Greek\", flag: \"🇬🇷\", accuracy: \"98.5%\" },\n    { name: \"Hebrew\", flag: \"🇮🇱\", accuracy: \"98.4%\" },\n    { name: \"Thai\", flag: \"🇹🇭\", accuracy: \"98.3%\" },\n    { name: \"Vietnamese\", flag: \"🇻🇳\", accuracy: \"98.2%\" },\n    { name: \"Indonesian\", flag: \"🇮🇩\", accuracy: \"98.4%\" }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-slate-800 dark:via-slate-900 dark:to-purple-900\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div ref={titleRef} className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Global Language Support</span>\n          </h2>\n          <p className=\"text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto\">\n            Break down language barriers with our comprehensive support for over 100 languages\n            and dialects, powered by advanced AI models trained on diverse datasets.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Globe Visualization */}\n          <div className=\"flex justify-center\">\n            <div ref={globeRef} className=\"relative w-80 h-80\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-20 blur-xl\"></div>\n              <div className=\"absolute inset-4 bg-gradient-to-br from-blue-500 to-purple-700 rounded-full flex items-center justify-center\">\n                <svg className=\"w-48 h-48 text-white opacity-80\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM11 19.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"/>\n                </svg>\n              </div>\n              <div className=\"absolute inset-0 border-4 border-blue-300 dark:border-blue-600 rounded-full animate-pulse\"></div>\n            </div>\n          </div>\n\n          {/* Languages Grid */}\n          <div ref={languagesRef} className=\"grid grid-cols-2 sm:grid-cols-3 gap-4\">\n            {languages.map((language, index) => (\n              <div\n                key={index}\n                className=\"group bg-white dark:bg-slate-800 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"text-3xl mb-2 group-hover:scale-110 transition-transform duration-300\">\n                    {language.flag}\n                  </div>\n                  <h3 className=\"font-semibold text-slate-800 dark:text-white mb-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300\">\n                    {language.name}\n                  </h3>\n                  <p className=\"text-sm text-green-600 dark:text-green-400 font-medium\">\n                    {language.accuracy}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mt-16 grid grid-cols-1 md:grid-cols-4 gap-8 text-center\">\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">100+</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Languages</div>\n          </div>\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">50+</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Dialects</div>\n          </div>\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">Auto</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Detection</div>\n          </div>\n          <div className=\"bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">Real-time</div>\n            <div className=\"text-slate-600 dark:text-slate-400\">Processing</div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AAElB,SAAS;;IACtB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,YAAY,aAAa,OAAO,CAAC,QAAQ;YAE/C,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SAAS,OAAO,EAC1B;gBAAE,GAAG;gBAAI,SAAS;YAAE,GACpB;gBACE,GAAG;gBACH,SAAS;gBACT,UAAU;gBACV,eAAe;oBACb,SAAS,SAAS,OAAO;oBACzB,OAAO;oBACP,KAAK;oBACL,eAAe;gBACjB;YACF;YAGF,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SAAS,OAAO,EAC1B;gBAAE,OAAO;gBAAK,SAAS;gBAAG,UAAU,CAAC;YAAI,GACzC;gBACE,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,eAAe;oBACb,SAAS,SAAS,OAAO;oBACzB,OAAO;oBACP,KAAK;oBACL,eAAe;gBACjB;YACF;YAGF,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,WACV;gBAAE,GAAG;gBAAI,SAAS;YAAE,GACpB;gBACE,GAAG;gBACH,SAAS;gBACT,UAAU;gBACV,SAAS;gBACT,MAAM;gBACN,eAAe;oBACb,SAAS,aAAa,OAAO;oBAC7B,OAAO;oBACP,KAAK;oBACL,eAAe;gBACjB;YACF;YAGF,gCAAgC;YAChC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS,OAAO,EAAE;gBACxB,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ,CAAC;YACX;QACF;qCAAG,EAAE;IAEL,MAAM,YAAY;QAChB;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAc,MAAM;YAAQ,UAAU;QAAQ;QACtD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAY,MAAM;YAAQ,UAAU;QAAQ;QACpD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAS,MAAM;YAAQ,UAAU;QAAQ;QACjD;YAAE,MAAM;YAAS,MAAM;YAAQ,UAAU;QAAQ;QACjD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAa,MAAM;YAAQ,UAAU;QAAQ;QACrD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAW,MAAM;YAAQ,UAAU;QAAQ;QACnD;YAAE,MAAM;YAAS,MAAM;YAAQ,UAAU;QAAQ;QACjD;YAAE,MAAM;YAAU,MAAM;YAAQ,UAAU;QAAQ;QAClD;YAAE,MAAM;YAAQ,MAAM;YAAQ,UAAU;QAAQ;QAChD;YAAE,MAAM;YAAc,MAAM;YAAQ,UAAU;QAAQ;QACtD;YAAE,MAAM;YAAc,MAAM;YAAQ,UAAU;QAAQ;KACvD;IAED,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,KAAK;oBAAU,WAAU;;sCAC5B,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,6LAAC;4BAAE,WAAU;sCAA+D;;;;;;;;;;;;8BAM9E,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAK;gCAAU,WAAU;;kDAC5B,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAkC,MAAK;4CAAe,SAAQ;sDAC3E,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAKnB,6LAAC;4BAAI,KAAK;4BAAc,WAAU;sCAC/B,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;oCAEC,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,SAAS,IAAI;;;;;;0DAEhB,6LAAC;gDAAG,WAAU;0DACX,SAAS,IAAI;;;;;;0DAEhB,6LAAC;gDAAE,WAAU;0DACV,SAAS,QAAQ;;;;;;;;;;;;mCAXjB;;;;;;;;;;;;;;;;8BAoBb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;8CAAqC;;;;;;;;;;;;sCAEtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;8CAAqC;;;;;;;;;;;;sCAEtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;8CAAqC;;;;;;;;;;;;sCAEtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhE;GAtKwB;KAAA", "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Ai%20tool/my-app/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport HeroSection from './components/HeroSection';\nimport FeaturesSection from './components/FeaturesSection';\nimport LanguagesSection from './components/LanguagesSection';\nimport HowItWorksSection from './components/HowItWorksSection';\nimport UseCasesSection from './components/UseCasesSection';\nimport CTASection from './components/CTASection';\nimport FAQSection from './components/FAQSection';\nimport Footer from './components/Footer';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function Home() {\n  useEffect(() => {\n    // Initialize GSAP animations\n    gsap.fromTo('.fade-in',\n      { opacity: 0, y: 50 },\n      {\n        opacity: 1,\n        y: 0,\n        duration: 1,\n        stagger: 0.2,\n        scrollTrigger: {\n          trigger: '.fade-in',\n          start: 'top 80%',\n          end: 'bottom 20%',\n          toggleActions: 'play none none reverse'\n        }\n      }\n    );\n  }, []);\n\n  return (\n    <main className=\"min-h-screen\">\n      <HeroSection />\n      <FeaturesSection />\n      <LanguagesSection />\n      <HowItWorksSection />\n      <UseCasesSection />\n      <CTASection />\n      <FAQSection />\n      <Footer />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPA;;;;;;;;;;;;AAcA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AAElB,SAAS;;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,6BAA6B;YAC7B,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,YACV;gBAAE,SAAS;gBAAG,GAAG;YAAG,GACpB;gBACE,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,SAAS;gBACT,eAAe;oBACb,SAAS;oBACT,OAAO;oBACP,KAAK;oBACL,eAAe;gBACjB;YACF;QAEJ;yBAAG,EAAE;IAEL,qBACE,6LAAC;QAAK,WAAU;;0BACd,6LAAC,0IAAA,CAAA,UAAW;;;;;0BACZ,6LAAC,8IAAA,CAAA,UAAe;;;;;0BAChB,6LAAC,+IAAA,CAAA,UAAgB;;;;;0BACjB,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;;;;;;;AAGP;GAhCwB;KAAA", "debugId": null}}]}